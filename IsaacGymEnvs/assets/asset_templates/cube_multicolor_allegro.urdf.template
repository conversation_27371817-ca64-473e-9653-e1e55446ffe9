<?xml version="1.0"?>
<robot name="object">
  <link name="object">
    <visual>
      <origin xyz="0 0 0"/>
      <geometry>
        <mesh filename="{{ base_mesh }}" scale="{{ x_scale }} {{ y_scale }} {{ z_scale }}"/>
      </geometry>
    </visual>
    <collision>
      <origin xyz="0 0 0"/>
      <geometry>
        <box size="{{ x_scale }} {{ y_scale }} {{ z_scale }}"/>
      </geometry>
    </collision>
    <inertial>
      <density value="400.0"/>
      <!-- <density value="{{ density }}"/> -->
    </inertial>
  </link>
</robot>

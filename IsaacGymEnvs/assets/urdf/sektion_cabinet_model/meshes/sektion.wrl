#VRML V2.0 utf8
Group {
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 0.975 0.486 0.975
          specularColor 0.7 0.7 1.0
        }
      }
      geometry IndexedFaceSet {
        solid FALSE
        coord Coordinate {
          point [
-0.284800 -0.381000 0.402000,
-0.284800 0.381000 0.402000,
-0.284800 0.381000 0.382000,
-0.284800 -0.381000 0.382000,
0.324800 0.381000 0.402000,
0.324800 0.381000 0.382000,
0.324800 -0.381000 0.402000,
0.324800 -0.381000 0.382000,
-0.286000 -0.362000 -0.362000,
-0.286000 0.362000 -0.362000,
-0.286000 0.362000 0.381000,
-0.286000 -0.362000 0.381000,
0.305000 0.381000 -0.381000,
0.305000 -0.381000 -0.381000,
-0.305000 -0.381000 -0.381000,
-0.305000 0.381000 -0.381000,
0.305000 0.362000 0.381000,
0.305000 0.381000 0.381000,
-0.305000 0.381000 0.381000,
-0.305000 -0.381000 0.381000,
0.305000 -0.381000 0.381000,
0.305000 -0.362000 0.381000,
0.305000 -0.362000 -0.362000,
0.305000 0.362000 -0.362000,
-0.292304 -0.362018 0.380000,
-0.292304 -0.362018 0.382000,
-0.292304 0.362042 0.382000,
-0.292304 0.362042 0.380000,
-0.265316 0.362042 0.382000,
-0.265316 0.362042 0.380000,
-0.266771 0.362113 0.380000,
-0.266771 0.362113 0.382000,
-0.265316 -0.362018 0.382000,
-0.265316 -0.362018 0.380000,
-0.290766 -0.362113 0.382000,
-0.266854 -0.362113 0.382000,
-0.266854 -0.362113 0.380000,
-0.290766 -0.362113 0.380000,
-0.266854 -0.380979 0.380000,
-0.290766 -0.380979 0.380000,
-0.266854 -0.380979 0.382000,
-0.290766 -0.380979 0.382000,
-0.266771 0.381007 0.380000,
-0.266771 0.381007 0.382000,
-0.290849 0.362113 0.380000,
-0.290849 0.362113 0.382000,
-0.290849 0.381007 0.380000,
-0.290849 0.381007 0.382000,
0.271506 -0.362018 0.380000,
0.271506 -0.362018 0.382000,
0.271506 0.362042 0.382000,
0.271506 0.362042 0.380000,
0.298494 0.362042 0.382000,
0.298494 0.362042 0.380000,
0.297039 0.362113 0.380000,
0.297039 0.362113 0.382000,
0.298494 -0.362018 0.382000,
0.298494 -0.362018 0.380000,
0.273044 -0.362113 0.382000,
0.296956 -0.362113 0.382000,
0.296956 -0.362113 0.380000,
0.273044 -0.362113 0.380000,
0.296956 -0.380979 0.380000,
0.273044 -0.380979 0.380000,
0.296956 -0.380979 0.382000,
0.273044 -0.380979 0.382000,
0.297039 0.381007 0.380000,
0.297039 0.381007 0.382000,
0.272961 0.362113 0.380000,
0.272961 0.362113 0.382000,
0.272961 0.381007 0.380000,
0.272961 0.381007 0.382000,

          ]
        }
        coordIndex [
0, 1, 2, -1,
0, 2, 3, -1,
1, 4, 5, -1,
1, 5, 2, -1,
4, 6, 7, -1,
4, 7, 5, -1,
6, 0, 3, -1,
6, 3, 7, -1,
3, 2, 5, -1,
3, 5, 7, -1,
6, 4, 1, -1,
6, 1, 0, -1,
8, 9, 10, -1,
8, 10, 11, -1,
12, 13, 14, -1,
12, 14, 15, -1,
16, 17, 10, -1,
10, 17, 18, -1,
10, 18, 11, -1,
11, 18, 19, -1,
11, 19, 20, -1,
11, 20, 21, -1,
22, 21, 20, -1,
22, 20, 13, -1,
22, 13, 12, -1,
22, 12, 23, -1,
16, 23, 12, -1,
16, 12, 17, -1,
13, 20, 19, -1,
13, 19, 14, -1,
14, 19, 18, -1,
14, 18, 15, -1,
17, 12, 15, -1,
17, 15, 18, -1,
23, 9, 8, -1,
23, 8, 22, -1,
10, 9, 23, -1,
10, 23, 16, -1,
22, 8, 11, -1,
22, 11, 21, -1,
24, 25, 26, -1,
24, 26, 27, -1,
28, 29, 30, -1,
28, 30, 31, -1,
29, 28, 32, -1,
29, 32, 33, -1,
32, 25, 34, -1,
32, 34, 35, -1,
27, 29, 33, -1,
27, 33, 24, -1,
28, 26, 25, -1,
28, 25, 32, -1,
33, 32, 35, -1,
33, 35, 36, -1,
25, 24, 37, -1,
25, 37, 34, -1,
24, 33, 36, -1,
24, 36, 37, -1,
37, 36, 38, -1,
37, 38, 39, -1,
38, 40, 41, -1,
38, 41, 39, -1,
34, 37, 39, -1,
34, 39, 41, -1,
35, 34, 41, -1,
35, 41, 40, -1,
36, 35, 40, -1,
36, 40, 38, -1,
31, 30, 42, -1,
31, 42, 43, -1,
29, 27, 44, -1,
29, 44, 30, -1,
26, 28, 31, -1,
26, 31, 45, -1,
27, 26, 45, -1,
27, 45, 44, -1,
46, 47, 43, -1,
46, 43, 42, -1,
30, 44, 46, -1,
30, 46, 42, -1,
45, 31, 43, -1,
45, 43, 47, -1,
44, 45, 47, -1,
44, 47, 46, -1,
48, 49, 50, -1,
48, 50, 51, -1,
52, 53, 54, -1,
52, 54, 55, -1,
53, 52, 56, -1,
53, 56, 57, -1,
56, 49, 58, -1,
56, 58, 59, -1,
51, 53, 57, -1,
51, 57, 48, -1,
52, 50, 49, -1,
52, 49, 56, -1,
57, 56, 59, -1,
57, 59, 60, -1,
49, 48, 61, -1,
49, 61, 58, -1,
48, 57, 60, -1,
48, 60, 61, -1,
61, 60, 62, -1,
61, 62, 63, -1,
62, 64, 65, -1,
62, 65, 63, -1,
58, 61, 63, -1,
58, 63, 65, -1,
59, 58, 65, -1,
59, 65, 64, -1,
60, 59, 64, -1,
60, 64, 62, -1,
55, 54, 66, -1,
55, 66, 67, -1,
53, 51, 68, -1,
53, 68, 54, -1,
50, 52, 55, -1,
50, 55, 69, -1,
51, 50, 69, -1,
51, 69, 68, -1,
70, 71, 67, -1,
70, 67, 66, -1,
54, 68, 70, -1,
54, 70, 66, -1,
69, 55, 67, -1,
69, 67, 71, -1,
68, 69, 71, -1,
68, 71, 70, -1

        ]
      }
    }
  ]
}

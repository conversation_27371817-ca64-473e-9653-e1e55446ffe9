<?xml version="1.0"?>
<robot name="cabinet_sektion">
  <material name="White"><color rgba="0.85 0.85 0.85 1.0"/></material>
  <material name="Grey"><color rgba="0.6 0.6 0.6 1.0"/></material>
  <material name="Front"><color rgba="0.4 0.4 0.4 1.0"/></material>


  <!--
  <link name="world"/>
  <joint name="world_to_sektion" type="fixed">
    <origin xyz="0.85 0 0.38" rpy="0 0 3.14159"/>
    <parent link="world"/>
    <child link="sektion"/>
  </joint>
  -->

  <link name="sektion">
    <!--inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial-->
    <!--
    <visual name="sektion_visual">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="White" />
    </visual>
    -->
    <collision name="sektion_collision0">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion_convexdecomposition0.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="sektion_collision1">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion_convexdecomposition1.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="sektion_collision2">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion_convexdecomposition2.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="sektion_collision3">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion_convexdecomposition3.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="sektion_collision4">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion_convexdecomposition4.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="sektion_collision5">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion_convexdecomposition5.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="sektion_collision6">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/sektion_convexdecomposition6.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
  </link>

  <link name="drawer_top">
    <visual name="drawer_top_visual">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Front" />
    </visual>
    <collision name="drawer_top_collision0">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition0.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="drawer_top_collision1">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition1.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="drawer_top_collision2">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition2.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="drawer_top_collision3">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition3.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="drawer_top_collision4">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition4.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="drawer_top_collision5">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition5.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <collision name="drawer_top_collision6">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition6.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
  </link>

  <link name="drawer_handle_top">

    <visual name="drawer_handle_top_visual">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_handle.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Grey" />
    </visual>
    <collision name="drawer_handle_top_collision">
      <!--<origin xyz="0.305 0 0.01" rpy="1.575 0 0"/>-->
<!--       <origin xyz="0.303 0 0.01" rpy="1.575 0 0"/>
      <geometry>
        <box size="0.014 0.02 0.15"/>
      </geometry> -->
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_handle_convexdecomposition.obj" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
<!--     <collision>
      <origin xyz="0.285 -0.07 0.01" rpy="0 0 0"/>
      <geometry>
        <box size="0.04 0.02 0.005"/>
      </geometry>
    </collision>
    <collision>
      <origin xyz="0.285 0.07 0.01" rpy="0 0 0"/>
      <geometry>
        <box size="0.04 0.02 0.005"/>
      </geometry>
    </collision> -->
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
    <contact>
      <lateral_friction value="2.0"/>
      <spinning_friction value="0.6"/>
      <inertia_scaling value="3.0"/>
    </contact>
  </link>

  <link name="drawer_bottom">
    <!--inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial-->
    <visual name="drawer_bottom_visual">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Front" />
    </visual>
    <collision name="drawer_bottom_collision">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_convexdecomposition.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="White" />
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
  </link>

  <link name="drawer_handle_bottom">
    <!--inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial-->
    <visual name="drawer_top_visual">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_handle.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Grey" />
    </visual>
    <collision name="drawer_top_collision">
      <origin rpy="0  0  0" xyz="0  0  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/drawer_handle_convexdecomposition.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="White" />
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
    <contact>
      <lateral_friction value="1.0"/>
      <spinning_friction value="0.3"/>
      <inertia_scaling value="3.0"/>
    </contact>
  </link>
<!--
  <link name="door_right_anchor_link" >
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.001"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <link name="door_right_joint_anchor_link" >
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.001"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
-->
  <link name="door_right_link">
    <!--inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial-->
    <visual name="door_right_visual">
      <origin rpy="0  0  0" xyz="0  -0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_right.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Front" />
    </visual>
    <collision name="door_right_collision">
      <origin rpy="0  0  0" xyz="0  -0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_right.stl" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="White" />
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
  </link>

  <link name="door_right_nob_link">
    <!--inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial-->
    <visual name="door_right_nob_visual">
      <origin rpy="0  0  0" xyz="0  -0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_right_nob.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Grey" />
    </visual>
    <collision name="door_right_nob_collision">
      <origin rpy="0  0  0" xyz="0  -0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_right_nob.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
  </link>
<!--
  <link name="door_right" />

  <link name="door_left_anchor_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.001"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
  <link name="door_left_joint_anchor_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.001"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>
-->
  <link name="door_left_link">
    <!--inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial-->
    <visual name="door_left_visual">
      <origin rpy="0  0  0" xyz="0  0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_left.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Front" />
    </visual>
    <collision name="door_left_collision">
      <origin rpy="0  0  0" xyz="0  0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_left.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
  </link>

  <link name="door_left_nob_link">
    <!--inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial-->
    <visual name="door_left_nob_visual">
      <origin rpy="0  0  0" xyz="0  0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_left_nob.obj" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="Grey" />
    </visual>
    <collision name="door_left_nob_collision">
      <origin rpy="0  0  0" xyz="0  0.184706  0"/>
      <geometry>
        <mesh filename="package://sektion_cabinet_model/meshes/door_left_nob.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <gazebo reference="link">
      <selfCollide>false</selfCollide>
    </gazebo>
  </link>
<!--
  <link name="door_left" />

  <joint name="door_right_joint_fixed" type="fixed">
    <parent link="sektion"/>
    <child link="door_right_anchor_link"/>
    <origin xyz="0.3162 0.3757 -0.12973" rpy="0 0 0"/>
  </joint>
  <joint name="door_right_shift_joint" type="prismatic">
    <parent link="door_right_anchor_link"/>
    <child link="door_right_joint_anchor_link"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1000.0" lower="-0.015" upper="0" velocity="0.0"/>
    <mimic joint="door_right_joint" multiplier="-0.009554" offset="0" />
    <dynamics damping="0.05" friction="0.025"/>
  </joint>
-->
  <joint name="door_right_joint" type="revolute">
    <parent link="sektion"/>
    <child link="door_right_link"/>
    <origin xyz="0.3162 0.3757 -0.12973" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="0" upper="1.57" velocity="0.1"/>
    <dynamics damping="0.05" friction="0.025"/>
  </joint>
  <!--
  <joint name="door_right_transform" type="fixed">
    <parent link="door_right_link"/>
    <child link="door_right"/>
    <origin xyz="0 -.181 0" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
  </joint>
  -->
  <joint name="door_right_nob_transform" type="fixed">
    <parent link="door_right_link"/>
    <child link="door_right_nob_link"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
  </joint>

<!--
  <joint name="door_left_joint_fixed" type="fixed">
    <parent link="sektion"/>
    <child link="door_left_anchor_link"/>
    <origin xyz="0.3162 -0.3757 -0.12973" rpy="0 0 0"/>
  </joint>
  <joint name="door_left_shift_joint" type="prismatic">
    <parent link="door_left_anchor_link"/>
    <child link="door_left_joint_anchor_link"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1000.0" lower="-0.015" upper="0" velocity="0.0"/>
    <mimic joint="door_left_joint" multiplier="-0.009554" offset="0" />
    <dynamics damping="0.05" friction="0.025"/>
  </joint>
-->
  <joint name="door_left_joint" type="revolute">
    <parent link="sektion"/>
    <child link="door_left_link"/>
    <origin xyz="0.3162 -0.3757 -0.12973" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="1000.0" lower="-1.57" upper="0." velocity="0.1"/>
    <dynamics damping="0.05" friction="0.025"/>
  </joint>
  <!--
  <joint name="door_left_transform" type="fixed">
    <parent link="door_left_link"/>
    <child link="door_left"/>
    <origin xyz="0 .181 0" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
  </joint>
  -->
  <joint name="door_left_nob_transform" type="fixed">
    <parent link="door_left_link"/>
    <child link="door_left_nob_link"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
  </joint>

  <joint name="drawer_top_joint" type="prismatic">
    <parent link="sektion"/>
    <child link="drawer_top"/>
    <origin xyz="0.0515 0 0.3172" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000.0" lower="0.0" upper="0.4" velocity="1."/>
    <dynamics damping="0.01" friction="0.01"/>
  </joint>

  <joint name="drawer_handle_top_joint" type="fixed">
    <parent link="drawer_top"/>
    <child link="drawer_handle_top"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000.0" lower="0.0" upper="0.4" velocity="0.1"/>
  </joint>
  
  <!--
  <link name="drawer_handle_top_grasp_frame" />
  <joint name="drawer_handle_top_grasp_frame_joint" type="fixed">
    <parent link="drawer_handle_top"/>
    <child link="drawer_handle_top_grasp_frame"/>
    <origin xyz="0.3 0 0.01" rpy="3.14159265359 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000.0" lower="0.0" upper="0.4" velocity="0.1"/>
  </joint>
  -->

  <joint name="drawer_bottom_joint" type="prismatic">
    <parent link="sektion"/>
    <child link="drawer_bottom"/>
    <origin xyz="0.0515 0 0.18795" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000.0" lower="0.0" upper="0.4" velocity="1.0"/>
    <dynamics damping="0.01" friction="0.01"/>
  </joint>

  <joint name="drawer_handle_bottom_joint" type="fixed">
    <parent link="drawer_bottom"/>
    <child link="drawer_handle_bottom"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000.0" lower="0.0" upper="0.4" velocity="0.1"/>
  </joint>
  <!--
  <link name="drawer_handle_bottom_grasp_frame" />
  <joint name="drawer_handle_bottom_grasp_frame_joint" type="fixed">
    <parent link="drawer_handle_bottom"/>
    <child link="drawer_handle_bottom_grasp_frame"/>
    <origin xyz="0.3 0 0.01" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000.0" lower="0.0" upper="0.4" velocity="0.1"/>
  </joint>
  -->

  <gazebo reference="door_left_shift_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="door_right_shift_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="door_left_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="door_right_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="drawer_top_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="drawer_bottom_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>

  <!--
  <transmission name="door_right_joint_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="door_right_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="door_right_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </actuator>
  </transmission>
  <transmission name="door_left_joint_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="door_left_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="door_left_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </actuator>
  </transmission>
  <transmission name="drawer_top_joint_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="drawer_top_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="drawer_top_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </actuator>
  </transmission>
  <transmission name="drawer_bottom_joint_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="drawer_bottom_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="drawer_bottom_motor">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </actuator>
  </transmission>
  -->

  <gazebo>
    <selfCollide>false</selfCollide>
    <material>Gazebo/Ivory</material>
    <plugin name="gazebo_ros_controller" filename="libgazebo_ros_control.so">
      <robotNamespace>/cabinet</robotNamespace>
      <robotParam>/cabinet_description</robotParam>
    </plugin>
  </gazebo>

</robot>

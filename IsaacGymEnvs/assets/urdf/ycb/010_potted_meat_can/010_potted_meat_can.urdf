<?xml version="1.0"?>
<robot name="potted_meat_can">
  <link name="potted_meat_can">
    <visual>
      <origin xyz="0.0 0.0 0.0"/>
      <geometry>
        <mesh filename="ycb/010_potted_meat_can/textured.obj" scale="0.01 0.01 0.01"/>
      </geometry>
    </visual>
    <collision>
      <origin xyz="0.0 0.0 0.0"/>
      <geometry>
        <mesh filename="ycb/010_potted_meat_can/collision.obj" scale="0.01 0.01 0.01"/>
      </geometry>
    </collision>
    <inertial>
      <!-- Mass value from the *empty* spam can. Full spam can is 368g -->
      <mass value="0.026"/> 
      <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
    </inertial>
  </link>
</robot>

<?xml version="1.0"?>
<robot name="cartpole">

    <link name="slider">
    <visual>
      <geometry>
        <box size="0.03 8 0.03"/>
      </geometry>
      <material name="slider_mat">
        <color rgba="0.9 0.6 0.2 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="0.03 8 0.03"/>
      </geometry>
    </collision>
  </link>

  <link name="cart">
    <visual>
      <geometry>
        <box size="0.2 0.25 0.2"/>
      </geometry>
      <material name="cart_mat">
        <color rgba="0.3 0.5 0.7 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
          <box size="0.2 0.25 0.2"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
    </inertial>
  </link>

  <link name="pole">
    <visual>
      <geometry>
        <box size="0.04 0.06 1.0"/>	
      </geometry>
      <origin xyz="0 0 0.47"/>
      <material name="pole_mat">
        <color rgba="0.1 0.1 0.3 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <box size="0.04 0.06 1.0"/>	
      </geometry>
      <origin xyz="0 0 0.47"/>
    </collision>
    <inertial>
      <mass value="1"/>
      <origin xyz="0 0 0.47"/>
    </inertial>
  </link>

  <joint name="slider_to_cart" type="prismatic">
    <axis xyz="0 1 0"/>
    <origin xyz="0 0 0"/>
    <parent link="slider"/>
    <child link="cart"/>
    <limit effort="1000.0" lower="-4" upper="4" velocity="100"/>
  </joint>

  <joint name="cart_to_pole" type="continuous">
    <axis xyz="1 0 0"/>
    <origin xyz="0.12 0 0"/>
    <parent link="cart"/>
    <child link="pole"/>
    <limit effort="1000.0" velocity="8"/>
  </joint>
  
</robot>

<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot_properties_fingers/xacro/pro/fingerpro.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="fingerpro">
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.

    In zero-configuration the origin of the base link is exactly above the
    finger tip which should make it easy to place the finger in the world.
    -->
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.

    In zero-configuration the origin of the base link is exactly above the
    finger tip which should make it easy to place the finger in the world.
    -->
  <material name="fingerpro_shell">
    <!-- colour from image -->
    <color rgba="0.804 0.764 0.761 1.0"/>
  </material>
  <material name="fingerpro_tip">
    <!-- colour from image -->
    <color rgba="0.33 0.36 0.37 1.0"/>
  </material>
  <material name="fingerpro_motor">
    <color rgba="0.17 0.17 0.17 1.0"/>
  </material>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        Note: Link Frames already coincide with the joint location
        -->
  <link name="finger_base_link">
    <inertial>
      <mass value="0"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
  </link>
  <link name="finger_upper_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/prox-sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="fingerpro_shell"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/prox-sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.006 0.122 0.000"/>
      <mass value="0.26689"/>
      <inertia ixx="0.00102362" ixy="0.00000889" ixz="-0.00000019" iyx="0.00000889" iyy="0.00006450" iyz="0.00000106" izx="-0.00000019" izy="0.00000106" izz="0.00102225"/>
    </inertial>
  </link>
  <link name="finger_upper_link_visuals">
    <!-- motor -->
    <visual>
      <origin xyz="0.01569 0.1643 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/motor_sim.stl"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <!-- end stop -->
    <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0.015 0.083 0"/>
      <geometry>
        <cylinder length="0.015" radius="0.005"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <!-- cables -->
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="-0.008 0.124 0"/>
      <geometry>
        <cylinder length="0.12" radius="0.002"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <!-- opening on lower joint -->
    <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="-0.0085 0.050 0"/>
      <geometry>
        <cylinder length="0.002" radius="0.014"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <inertial>
      <mass value="0"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
  </link>
  <joint name="finger_upper_visuals_joint" type="fixed">
    <parent link="finger_upper_link"/>
    <child link="finger_upper_link_visuals"/>
    <origin xyz="0 0 0"/>
  </joint>
  <link name="finger_middle_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/int_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="fingerpro_shell"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/int_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.03935008 -0.00012438 -0.08666705"/>
      <mass value="0.27163"/>
      <inertia ixx="0.00094060" ixy="-0.00000046" ixz="-0.00003479" iyx="-0.00000046" iyy="0.00094824" iyz="0.00000164" izx="-0.00003479" izy="0.00000164" izz="0.00007573"/>
    </inertial>
  </link>
  <link name="finger_middle_link_visuals">
    <!-- motor -->
    <visual>
      <origin xyz="0.0488 0 -0.0462"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/motor_sim.stl"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <!-- end stop -->
    <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0.0488 0 -0.13"/>
      <geometry>
        <cylinder length="0.015" radius="0.005"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <!-- cables -->
    <visual>
      <origin xyz="0.025 0 -0.09"/>
      <geometry>
        <cylinder length="0.12" radius="0.002"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <!-- opening on lower joint -->
    <visual>
      <origin rpy="0 1.5707963267948966 0" xyz="0.024 0 -0.16"/>
      <geometry>
        <cylinder length="0.002" radius="0.014"/>
      </geometry>
      <material name="fingerpro_motor"/>
    </visual>
    <inertial>
      <mass value="0"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
  </link>
  <joint name="finger_middle_visuals_joint" type="fixed">
    <parent link="finger_middle_link"/>
    <child link="finger_middle_link_visuals"/>
  </joint>
  <link name="finger_lower_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/tip_link_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="fingerpro_shell"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/tip_link_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.01632700 -0.00001095 -0.04284052"/>
      <mass value="0.05569"/>
      <inertia ixx="0.00013626" ixy="0.00000000" ixz="-0.00000662" iyx="0.00000000" iyy="0.00013372" iyz="0.00000004" izx="-0.00000662" izy="0.00000004" izz="0.00000667"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/tip_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="fingerpro_tip"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/tip_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.031"/>
      <inertia ixx="5.166666666666667e-07" ixy="0" ixz="0" iyy="5.166666666666667e-07" iyz="0" izz="5.166666666666667e-07"/>
    </inertial>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00001194 0.00000000 0.01560060"/>
      <mass value="0.0092"/>
      <inertia ixx="0.00000155" ixy="0.00000000" ixz="0.00000000" iyx="0.00000000" iyy="0.00000155" iyz="0.00000000" izx="0.00000000" izy="0.00000000" izz="0.00000032"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint" type="fixed">
    <parent link="finger_lower_link"/>
    <child link="finger_tip_link"/>
    <origin xyz="0.019 0 -0.16"/>
  </joint>
  <!-- kinematics -->
  <!-- NOTE: Joint limits are set according to soft limits in the
             configuration of the real robot (i.e. not according to physical
             limits). -->
  <joint name="finger_base_to_upper_joint" type="revolute">
    <parent link="finger_base_link"/>
    <child link="finger_upper_link"/>
    <limit effort="1000" lower="-0.33" upper="1.0" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint" type="revolute">
    <parent link="finger_upper_link"/>
    <child link="finger_middle_link"/>
    <limit effort="1000" lower="0.0" upper="1.57" velocity="1000"/>
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.01685 0.0505 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint" type="revolute">
    <parent link="finger_middle_link"/>
    <child link="finger_lower_link"/>
    <limit effort="1000" lower="-2.7" upper="0.0" velocity="1000"/>
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0.05015 0 -0.16"/>
  </joint>
  <!--
    Define the global base link and place the finger relative to it.
    Note: Here I've used the center point of the center piece as the origin 
    -->
  <link name="base_link">
    <!-- add inertial to suppress warning -->
    <inertial>
      <mass value="0"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
  </link>
  <link name="center_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/center_axis_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
      <material name="fingerpro_shell"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/pro/center_axis_sim.stl" scale="1.0 1.0 1.0"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="4.166666666666667e-06" ixy="0" ixz="0" iyy="4.166666666666667e-06" iyz="0" izz="4.166666666666667e-06"/>
    </inertial>
  </link>
  <joint name="base_to_center_joint" type="fixed">
    <parent link="base_link"/>
    <child link="center_link"/>
    <origin xyz="0 0 0.29"/>
  </joint>
  <joint name="base_to_finger" type="fixed">
    <parent link="center_link"/>
    <child link="finger_base_link"/>
    <origin xyz="0 0 0"/>
  </joint>
</robot>

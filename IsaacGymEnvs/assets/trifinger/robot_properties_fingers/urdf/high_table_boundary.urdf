<?xml version="1.0" encoding="utf-8"?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /home/<USER>/ws/fingers/workspace/src/catkin/robots/robot_properties/robot_properties_fingers/xacro/stage.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="stage">
  <material name="stage_material">
    <color rgba="0.8 0.8 0.8 1.0"/>
  </material>
  <!-- add the "stage" -->
  <link name="stage_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/high_table_boundary.stl" scale="1 1 1"/>
      </geometry>
      <material name="stage_material"/>
    </visual>

    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/high_table_boundary.stl" scale="1 1 1"/>
      </geometry>
    </collision>

    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="0.00770833333333" ixy="0" ixz="0" iyy="0.00770833333333" iyz="0" izz="0.015"/>
    </inertial>
  </link>
</robot>

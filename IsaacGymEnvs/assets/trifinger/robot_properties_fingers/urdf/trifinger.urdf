<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot_properties_fingers/xacro/trifinger.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="finger_with_stage">
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.

    In zero-configuration the origin of the base link is exactly above the
    finger tip which should make it easy to place the finger in the world.
    -->
  <!-- Define the global base_link and place all other objects relative to it. -->
  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="4.166666666666667e-06" ixy="0" ixz="0" iyy="4.166666666666667e-06" iyz="0" izz="4.166666666666667e-06"/>
    </inertial>
  </link>
  <!-- Define the upper holder -->
  <link name="upper_holder_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="4.166666666666667e-06" ixy="0" ixz="0" iyy="4.166666666666667e-06" iyz="0" izz="4.166666666666667e-06"/>
    </inertial>
  </link>
  <joint name="base_to_upper_holder_joint" type="fixed">
    <parent link="base_link"/>
    <child link="upper_holder_link"/>
    <origin xyz="0 0 0.29"/>
  </joint>
  <material name="finger_0_material">
    <color rgba="0.6 0.0 0.0 1.0"/>
  </material>
  <material name="finger_120_material">
    <color rgba="0.0 0.6 0.0 1.0"/>
  </material>
  <material name="finger_240_material">
    <color rgba="0.0 0.0 0.6 1.0"/>
  </material>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link_0">
    <visual>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="0.00041666666666666675" ixy="0" ixz="0" iyy="0.00041666666666666675" iyz="0" izz="0.00041666666666666675"/>
    </inertial>
  </link>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- TODO inertia parameters don't look correct, they cant be same for upper and middle! -->
  <link name="finger_upper_link_0">
    <visual>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.06 0"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="5.333333333333333e-05" iyz="0" izz="0.0003533333333333334"/>
    </inertial>
  </link>
  <link name="finger_middle_link_0">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_0_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.028 0 -0.08"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="0.0003533333333333334" iyz="0" izz="5.333333333333333e-05"/>
    </inertial>
  </link>
  <link name="finger_lower_link_0">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_0_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 -0.06"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666667e-05" ixy="0" ixz="0" iyy="1.6666666666666667e-05" iyz="0" izz="6.666666666666667e-07"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link_0">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.02"/>
      <inertia ixx="3.3333333333333335e-07" ixy="0" ixz="0" iyy="3.3333333333333335e-07" iyz="0" izz="3.3333333333333335e-07"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint_0" type="fixed">
    <parent link="finger_lower_link_0"/>
    <child link="finger_tip_link_0"/>
    <origin xyz="0 0 -0.16"/>
  </joint>
  <!-- kinematics -->
  <!-- TODO: joint limits are based on values from wiki
        (https://atlas.is.localnet/confluence/display/AMDW/Forward+Kinematics) but
        should be verified on the real system (especially the finger tip limits look
        a bit too big when visualizing the model. -->
  <joint name="finger_base_to_upper_joint_0" type="revolute">
    <parent link="finger_base_link_0"/>
    <child link="finger_upper_link_0"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint_0" type="revolute">
    <parent link="finger_upper_link_0"/>
    <child link="finger_middle_link_0"/>
    <limit effort="1000" lower="-1.3526301702956054" upper="4.494222823885399" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint_0" type="revolute">
    <parent link="finger_middle_link_0"/>
    <child link="finger_lower_link_0"/>
    <limit effort="1000" lower="-3.001966313430247" upper="3.001966313430247" velocity="1000"/>
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 -0.16"/>
  </joint>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link_120">
    <visual>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="0.00041666666666666675" ixy="0" ixz="0" iyy="0.00041666666666666675" iyz="0" izz="0.00041666666666666675"/>
    </inertial>
  </link>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- TODO inertia parameters don't look correct, they cant be same for upper and middle! -->
  <link name="finger_upper_link_120">
    <visual>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.06 0"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="5.333333333333333e-05" iyz="0" izz="0.0003533333333333334"/>
    </inertial>
  </link>
  <link name="finger_middle_link_120">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_120_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.028 0 -0.08"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="0.0003533333333333334" iyz="0" izz="5.333333333333333e-05"/>
    </inertial>
  </link>
  <link name="finger_lower_link_120">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_120_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 -0.06"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666667e-05" ixy="0" ixz="0" iyy="1.6666666666666667e-05" iyz="0" izz="6.666666666666667e-07"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link_120">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.02"/>
      <inertia ixx="3.3333333333333335e-07" ixy="0" ixz="0" iyy="3.3333333333333335e-07" iyz="0" izz="3.3333333333333335e-07"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint_120" type="fixed">
    <parent link="finger_lower_link_120"/>
    <child link="finger_tip_link_120"/>
    <origin xyz="0 0 -0.16"/>
  </joint>
  <!-- kinematics -->
  <!-- TODO: joint limits are based on values from wiki
        (https://atlas.is.localnet/confluence/display/AMDW/Forward+Kinematics) but
        should be verified on the real system (especially the finger tip limits look
        a bit too big when visualizing the model. -->
  <joint name="finger_base_to_upper_joint_120" type="revolute">
    <parent link="finger_base_link_120"/>
    <child link="finger_upper_link_120"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint_120" type="revolute">
    <parent link="finger_upper_link_120"/>
    <child link="finger_middle_link_120"/>
    <limit effort="1000" lower="-1.3526301702956054" upper="4.494222823885399" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint_120" type="revolute">
    <parent link="finger_middle_link_120"/>
    <child link="finger_lower_link_120"/>
    <limit effort="1000" lower="-3.001966313430247" upper="3.001966313430247" velocity="1000"/>
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 -0.16"/>
  </joint>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link_240">
    <visual>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="0.00041666666666666675" ixy="0" ixz="0" iyy="0.00041666666666666675" iyz="0" izz="0.00041666666666666675"/>
    </inertial>
  </link>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- TODO inertia parameters don't look correct, they cant be same for upper and middle! -->
  <link name="finger_upper_link_240">
    <visual>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.06 0"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="5.333333333333333e-05" iyz="0" izz="0.0003533333333333334"/>
    </inertial>
  </link>
  <link name="finger_middle_link_240">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_240_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.028 0 -0.08"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="0.0003533333333333334" iyz="0" izz="5.333333333333333e-05"/>
    </inertial>
  </link>
  <link name="finger_lower_link_240">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_240_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 -0.06"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666667e-05" ixy="0" ixz="0" iyy="1.6666666666666667e-05" iyz="0" izz="6.666666666666667e-07"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link_240">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.02"/>
      <inertia ixx="3.3333333333333335e-07" ixy="0" ixz="0" iyy="3.3333333333333335e-07" iyz="0" izz="3.3333333333333335e-07"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint_240" type="fixed">
    <parent link="finger_lower_link_240"/>
    <child link="finger_tip_link_240"/>
    <origin xyz="0 0 -0.16"/>
  </joint>
  <!-- kinematics -->
  <!-- TODO: joint limits are based on values from wiki
        (https://atlas.is.localnet/confluence/display/AMDW/Forward+Kinematics) but
        should be verified on the real system (especially the finger tip limits look
        a bit too big when visualizing the model. -->
  <joint name="finger_base_to_upper_joint_240" type="revolute">
    <parent link="finger_base_link_240"/>
    <child link="finger_upper_link_240"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint_240" type="revolute">
    <parent link="finger_upper_link_240"/>
    <child link="finger_middle_link_240"/>
    <limit effort="1000" lower="-1.3526301702956054" upper="4.494222823885399" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint_240" type="revolute">
    <parent link="finger_middle_link_240"/>
    <child link="finger_lower_link_240"/>
    <limit effort="1000" lower="-3.001966313430247" upper="3.001966313430247" velocity="1000"/>
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 -0.16"/>
  </joint>
  <joint name="holder_to_finger_0" type="fixed">
    <parent link="upper_holder_link"/>
    <child link="finger_base_link_0"/>
    <origin rpy="0 0 0" xyz="0.0 0.04 0"/>
  </joint>
  <joint name="holder_to_finger_120" type="fixed">
    <parent link="upper_holder_link"/>
    <child link="finger_base_link_120"/>
    <origin rpy="0 0 -2.0943951023931953" xyz="0.034641016151377546 -0.01999999999999999 0"/>
  </joint>
  <joint name="holder_to_finger_240" type="fixed">
    <parent link="upper_holder_link"/>
    <child link="finger_base_link_240"/>
    <origin rpy="0 0 -4.1887902047863905" xyz="-0.03464101615137754 -0.020000000000000018 0"/>
  </joint>
</robot>

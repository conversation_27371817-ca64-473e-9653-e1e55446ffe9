<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot_properties_fingers/xacro/finger.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="finger">
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.

    In zero-configuration the origin of the base link is exactly above the
    finger tip which should make it easy to place the finger in the world.
    -->
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.

    In zero-configuration the origin of the base link is exactly above the
    finger tip which should make it easy to place the finger in the world.
    -->
  <material name="finger_material">
    <color rgba="0.6 0.6 0.6 1.0"/>
  </material>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0.179 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Base.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="0.00041666666666666675" ixy="0" ixz="0" iyy="0.00041666666666666675" iyz="0" izz="0.00041666666666666675"/>
    </inertial>
  </link>
  <!-- base_holder is added in case of single finger -->
  <link name="finger_upper_holder_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/BL_Finger_Holder_SIM.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/BL_Finger_Holder_SIM.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!--
                  for some reason this link needs to have some inertia defined,
                  otherwise the model won't show up in gazebo
                -->
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.00020833333333333337" ixy="0" ixz="0" iyy="0.00020833333333333337" iyz="0" izz="0.00020833333333333337"/>
    </inertial>
  </link>
  <joint name="finger_base_to_holder" type="fixed">
    <parent link="finger_base_link"/>
    <child link="finger_upper_holder_link"/>
    <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
  </joint>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- TODO inertia parameters don't look correct, they cant be same for upper and middle! -->
  <link name="finger_upper_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 -0.02695 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Proximal.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.06 0"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="5.333333333333333e-05" iyz="0" izz="0.0003533333333333334"/>
    </inertial>
  </link>
  <link name="finger_middle_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM__BL-Finger_Intermediate.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.028 0 -0.08"/>
      <mass value="0.2"/>
      <inertia ixx="0.0003533333333333334" ixy="0" ixz="0" iyy="0.0003533333333333334" iyz="0" izz="5.333333333333333e-05"/>
    </inertial>
  </link>
  <link name="finger_lower_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_material"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0.014 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/SIM_BL_FINGER_TIP_LINK.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.16"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/finger_padding.stl" scale="1 1 1"/>
      </geometry>
      <material name="finger_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.16"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/finger_padding.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 -0.06"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666667e-05" ixy="0" ixz="0" iyy="1.6666666666666667e-05" iyz="0" izz="6.666666666666667e-07"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.002"/>
      <inertia ixx="3.3333333333333334e-08" ixy="0" ixz="0" iyy="3.3333333333333334e-08" iyz="0" izz="3.3333333333333334e-08"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint" type="fixed">
    <parent link="finger_lower_link"/>
    <child link="finger_tip_link"/>
    <origin xyz="0 0 -0.16"/>
  </joint>
  <!-- kinematics -->
  <!-- TODO: joint limits are based on values from wiki
        (https://atlas.is.localnet/confluence/display/AMDW/Forward+Kinematics) but
        should be verified on the real system (especially the finger tip limits look
        a bit too big when visualizing the model. -->
  <joint name="finger_base_to_upper_joint" type="revolute">
    <parent link="finger_base_link"/>
    <child link="finger_upper_link"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint" type="revolute">
    <parent link="finger_upper_link"/>
    <child link="finger_middle_link"/>
    <limit effort="1000" lower="-1.3526301702956054" upper="4.494222823885399" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint" type="revolute">
    <parent link="finger_middle_link"/>
    <child link="finger_lower_link"/>
    <limit effort="1000" lower="-3.001966313430247" upper="3.001966313430247" velocity="1000"/>
    <axis xyz="1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 -0.16"/>
  </joint>
  <!--
    Define the global base link and place the finger relative to it.
    -->
  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="4.166666666666667e-06" ixy="0" ixz="0" iyy="4.166666666666667e-06" iyz="0" izz="4.166666666666667e-06"/>
    </inertial>
  </link>
  <joint name="base_to_finger" type="fixed">
    <parent link="base_link"/>
    <child link="finger_base_link"/>
    <origin xyz="0 0 0.34"/>
  </joint>
</robot>

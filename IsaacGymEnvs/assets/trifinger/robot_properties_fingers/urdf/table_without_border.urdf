<?xml version="1.0" encoding="utf-8"?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /home/<USER>/ws/fingers/workspace/src/catkin/robots/robot_properties/robot_properties_fingers/xacro/trifinger_stage.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="trifinger_stage">
  <link name="base_link">
  </link>
  <material name="table_material">
    <color rgba="0.31 0.27 0.25 1.0"/>
  </material>
  <link name="table_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/trifinger_table_without_border.stl" scale="1 1 1"/>
      </geometry>
      <material name="table_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/trifinger_table_without_border.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.0962833333333" ixy="0" ixz="0" iyy="0.0840333333333" iyz="0" izz="0.180283333333"/>
    </inertial>
  </link>
  <joint name="base_to_table" type="fixed">
    <parent link="base_link"/>
    <child link="table_link"/>
    <origin xyz="0 0 0"/>
  </joint>
</robot>

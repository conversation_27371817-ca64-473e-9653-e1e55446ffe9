<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot_properties_fingers/xacro/edu/trifingeredu_stage.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="trifingeredu_stage">
  <material name="boundary_material">
    <color rgba="0.95 0.95 0.95 1.0"/>
  </material>
  <link name="table_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/stl/trifinger_table_without_border.stl" scale="1 1 1"/>
      </geometry>
      <material name="boundary_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/stl/trifinger_table_without_border.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.09628333333333333" ixy="0" ixz="0" iyy="0.08403333333333332" iyz="0" izz="0.18028333333333335"/>
    </inertial>
  </link>
  <link name="boundary_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/stl/edu/frame_wall.stl" scale="1 1 1"/>
      </geometry>
      <material name="boundary_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/stl/edu/frame_wall.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.05708333333333333" ixy="0" ixz="0" iyy="0.05708333333333333" iyz="0" izz="0.10453333333333334"/>
    </inertial>
  </link>
  <joint name="base_to_table" type="fixed">
    <parent link="base_link"/>
    <child link="table_link"/>
    <origin xyz="0 0 0"/>
  </joint>
  <joint name="table_to_boundary" type="fixed">
    <parent link="table_link"/>
    <child link="boundary_link"/>
    <origin xyz="0 0 0"/>
  </joint>
</robot>

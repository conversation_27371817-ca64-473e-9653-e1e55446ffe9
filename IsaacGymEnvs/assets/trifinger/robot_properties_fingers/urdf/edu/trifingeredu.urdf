<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot_properties_fingers/xacro/edu/trifingeredu.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="trifingeredu">
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.
    -->
  <!-- Define the global base_link and place all other objects relative to it. -->
  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="4.166666666666667e-06" ixy="0" ixz="0" iyy="4.166666666666667e-06" iyz="0" izz="4.166666666666667e-06"/>
    </inertial>
  </link>
  <!-- Define the upper holder -->
  <link name="trifinger_base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="4.166666666666667e-06" ixy="0" ixz="0" iyy="4.166666666666667e-06" iyz="0" izz="4.166666666666667e-06"/>
    </inertial>
  </link>
  <joint name="base_to_trifinger_base_joint" type="fixed">
    <parent link="base_link"/>
    <child link="trifinger_base_link"/>
    <origin rpy="0 0 -1.5707963267948966" xyz="0 0 0.283"/>
  </joint>
  <material name="fingeredu_0_material">
    <color rgba="0.6 0.0 0.0 1.0"/>
  </material>
  <material name="fingeredu_120_material">
    <color rgba="0.0 0.6 0.0 1.0"/>
  </material>
  <material name="fingeredu_240_material">
    <color rgba="0.0 0.0 0.6 1.0"/>
  </material>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link_0">
    <visual>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- Disable this part.  It is not very relevant for collisions but
                 would cause trouble in pybullet due to its concavity.
            <xacro:add_geometry
                rpy="0 0 0"
                xyz="${offset_x_base_to_front} -0.02 ${offset_z_base_to_top}"
                mesh_file="${mesh_dir}/base_side_right.stl"
                material="${material}" />
            -->
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.1 0 0.4"/>
      <mass value="1"/>
      <inertia ixx="0.060833333333333336" ixy="0" ixz="0" iyy="0.05666666666666668" iyz="0" izz="0.010833333333333334"/>
    </inertial>
  </link>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- FIXME inertias are not correct! -->
  <link name="finger_upper_link_0">
    <visual>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <!-- Note: This uses the values from the middle link, assuming
                     that it is similar enough. -->
      <!-- CoM is only estimated based -->
      <origin rpy="0 0 0" xyz="-0.079 0 0"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00003" ixy="0.00005" ixz="0.00000" iyy="0.00041" iyz="0.00000" izz="0.00041"/>
    </inertial>
  </link>
  <link name="finger_middle_link_0">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.019 -0.079"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00041" ixy="0.00000" ixz="0.00000" iyy="0.00041" iyz="0.00005" izz="0.00003"/>
    </inertial>
  </link>
  <link name="finger_lower_link_0">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_0_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- TODO: these are the interial values from the Solo lower leg
                 link which is similar but not exactly the same to the FingerEdu
                 lower link. -->
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.009 -0.089"/>
      <mass value="0.03070"/>
      <inertia ixx="0.00012" ixy="0.00000" ixz="0.00000" iyy="0.00012" iyz="0.00000" izz="0.00000"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link_0">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666668e-07" ixy="0" ixz="0" iyy="1.6666666666666668e-07" iyz="0" izz="1.6666666666666668e-07"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint_0" type="fixed">
    <parent link="finger_lower_link_0"/>
    <child link="finger_tip_link_0"/>
    <origin xyz="0 -0.008 -0.16"/>
  </joint>
  <!-- kinematics -->
  <joint name="finger_base_to_upper_joint_0" type="revolute">
    <parent link="finger_base_link_0"/>
    <child link="finger_upper_link_0"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint_0" type="revolute">
    <parent link="finger_upper_link_0"/>
    <child link="finger_middle_link_0"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.014 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint_0" type="revolute">
    <parent link="finger_middle_link_0"/>
    <child link="finger_lower_link_0"/>
    <limit effort="1000" lower="-3.141592653589793" upper="3.141592653589793" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.03745 -0.16"/>
  </joint>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link_120">
    <visual>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- Disable this part.  It is not very relevant for collisions but
                 would cause trouble in pybullet due to its concavity.
            <xacro:add_geometry
                rpy="0 0 0"
                xyz="${offset_x_base_to_front} -0.02 ${offset_z_base_to_top}"
                mesh_file="${mesh_dir}/base_side_right.stl"
                material="${material}" />
            -->
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.1 0 0.4"/>
      <mass value="1"/>
      <inertia ixx="0.060833333333333336" ixy="0" ixz="0" iyy="0.05666666666666668" iyz="0" izz="0.010833333333333334"/>
    </inertial>
  </link>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- FIXME inertias are not correct! -->
  <link name="finger_upper_link_120">
    <visual>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <!-- Note: This uses the values from the middle link, assuming
                     that it is similar enough. -->
      <!-- CoM is only estimated based -->
      <origin rpy="0 0 0" xyz="-0.079 0 0"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00003" ixy="0.00005" ixz="0.00000" iyy="0.00041" iyz="0.00000" izz="0.00041"/>
    </inertial>
  </link>
  <link name="finger_middle_link_120">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.019 -0.079"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00041" ixy="0.00000" ixz="0.00000" iyy="0.00041" iyz="0.00005" izz="0.00003"/>
    </inertial>
  </link>
  <link name="finger_lower_link_120">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_120_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- TODO: these are the interial values from the Solo lower leg
                 link which is similar but not exactly the same to the FingerEdu
                 lower link. -->
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.009 -0.089"/>
      <mass value="0.03070"/>
      <inertia ixx="0.00012" ixy="0.00000" ixz="0.00000" iyy="0.00012" iyz="0.00000" izz="0.00000"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link_120">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666668e-07" ixy="0" ixz="0" iyy="1.6666666666666668e-07" iyz="0" izz="1.6666666666666668e-07"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint_120" type="fixed">
    <parent link="finger_lower_link_120"/>
    <child link="finger_tip_link_120"/>
    <origin xyz="0 -0.008 -0.16"/>
  </joint>
  <!-- kinematics -->
  <joint name="finger_base_to_upper_joint_120" type="revolute">
    <parent link="finger_base_link_120"/>
    <child link="finger_upper_link_120"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint_120" type="revolute">
    <parent link="finger_upper_link_120"/>
    <child link="finger_middle_link_120"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.014 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint_120" type="revolute">
    <parent link="finger_middle_link_120"/>
    <child link="finger_lower_link_120"/>
    <limit effort="1000" lower="-3.141592653589793" upper="3.141592653589793" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.03745 -0.16"/>
  </joint>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link_240">
    <visual>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- Disable this part.  It is not very relevant for collisions but
                 would cause trouble in pybullet due to its concavity.
            <xacro:add_geometry
                rpy="0 0 0"
                xyz="${offset_x_base_to_front} -0.02 ${offset_z_base_to_top}"
                mesh_file="${mesh_dir}/base_side_right.stl"
                material="${material}" />
            -->
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.1 0 0.4"/>
      <mass value="1"/>
      <inertia ixx="0.060833333333333336" ixy="0" ixz="0" iyy="0.05666666666666668" iyz="0" izz="0.010833333333333334"/>
    </inertial>
  </link>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- FIXME inertias are not correct! -->
  <link name="finger_upper_link_240">
    <visual>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <!-- Note: This uses the values from the middle link, assuming
                     that it is similar enough. -->
      <!-- CoM is only estimated based -->
      <origin rpy="0 0 0" xyz="-0.079 0 0"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00003" ixy="0.00005" ixz="0.00000" iyy="0.00041" iyz="0.00000" izz="0.00041"/>
    </inertial>
  </link>
  <link name="finger_middle_link_240">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.019 -0.079"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00041" ixy="0.00000" ixz="0.00000" iyy="0.00041" iyz="0.00005" izz="0.00003"/>
    </inertial>
  </link>
  <link name="finger_lower_link_240">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_240_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- TODO: these are the interial values from the Solo lower leg
                 link which is similar but not exactly the same to the FingerEdu
                 lower link. -->
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.009 -0.089"/>
      <mass value="0.03070"/>
      <inertia ixx="0.00012" ixy="0.00000" ixz="0.00000" iyy="0.00012" iyz="0.00000" izz="0.00000"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link_240">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666668e-07" ixy="0" ixz="0" iyy="1.6666666666666668e-07" iyz="0" izz="1.6666666666666668e-07"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint_240" type="fixed">
    <parent link="finger_lower_link_240"/>
    <child link="finger_tip_link_240"/>
    <origin xyz="0 -0.008 -0.16"/>
  </joint>
  <!-- kinematics -->
  <joint name="finger_base_to_upper_joint_240" type="revolute">
    <parent link="finger_base_link_240"/>
    <child link="finger_upper_link_240"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint_240" type="revolute">
    <parent link="finger_upper_link_240"/>
    <child link="finger_middle_link_240"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.014 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint_240" type="revolute">
    <parent link="finger_middle_link_240"/>
    <child link="finger_lower_link_240"/>
    <limit effort="1000" lower="-3.141592653589793" upper="3.141592653589793" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.03745 -0.16"/>
  </joint>
  <joint name="base_to_finger_0" type="fixed">
    <parent link="trifinger_base_link"/>
    <child link="finger_base_link_0"/>
    <origin rpy="0 0 0" xyz="-0.0455 0.0 0"/>
  </joint>
  <joint name="base_to_finger_120" type="fixed">
    <parent link="trifinger_base_link"/>
    <child link="finger_base_link_120"/>
    <origin rpy="0 0 -2.0943951023931953" xyz="0.02274999999999999 0.03940415587219196 0"/>
  </joint>
  <joint name="base_to_finger_240" type="fixed">
    <parent link="trifinger_base_link"/>
    <child link="finger_base_link_240"/>
    <origin rpy="0 0 -4.1887902047863905" xyz="0.02275000000000002 -0.03940415587219195 0"/>
  </joint>
</robot>

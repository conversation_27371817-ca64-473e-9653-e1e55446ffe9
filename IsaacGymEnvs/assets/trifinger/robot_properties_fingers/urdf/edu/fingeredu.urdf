<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot_properties_fingers/xacro/edu/fingeredu.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="fingeredu">
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.

    In zero-configuration the origin of the base link is exactly above the
    finger tip which should make it easy to place the finger in the world.
    -->
  <!--
    Frames are defined such that they all align with the base frame when all
    joints are at position zero.

    In zero-configuration the origins of all joints are in one line.
    -->
  <material name="fingeredu_material">
    <color rgba="0.6 0.6 0.6 1.0"/>
  </material>
  <!--
        Fixed links for the finger base (parts where the upper link is mounted).
        -->
  <link name="finger_base_link">
    <visual>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="-0.17995 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_back.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_front.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0.02 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_side_left.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- Disable this part.  It is not very relevant for collisions but
                 would cause trouble in pybullet due to its concavity.
            <xacro:add_geometry
                rpy="0 0 0"
                xyz="${offset_x_base_to_front} -0.02 ${offset_z_base_to_top}"
                mesh_file="${mesh_dir}/base_side_right.stl"
                material="${material}" />
            -->
    <visual>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.025500000000000002 0 0.08"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/base_top.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.1 0 0.4"/>
      <mass value="1"/>
      <inertia ixx="0.060833333333333336" ixy="0" ixz="0" iyy="0.05666666666666668" iyz="0" izz="0.010833333333333334"/>
    </inertial>
  </link>
  <!-- The movable links (upper, middle and lower) of the finger. -->
  <!-- FIXME inertias are not correct! -->
  <link name="finger_upper_link">
    <visual>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.0195 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/upper_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <!-- Note: This uses the values from the middle link, assuming
                     that it is similar enough. -->
      <!-- CoM is only estimated based -->
      <origin rpy="0 0 0" xyz="-0.079 0 0"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00003" ixy="0.00005" ixz="0.00000" iyy="0.00041" iyz="0.00000" izz="0.00041"/>
    </inertial>
  </link>
  <link name="finger_middle_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/middle_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.019 -0.079"/>
      <mass value="0.14854"/>
      <inertia ixx="0.00041" ixy="0.00000" ixz="0.00000" iyy="0.00041" iyz="0.00005" izz="0.00003"/>
    </inertial>
  </link>
  <link name="finger_lower_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
      <material name="fingeredu_material"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://robot_properties_fingers/meshes/edu/lower_link.stl" scale="1 1 1"/>
      </geometry>
    </collision>
    <!-- TODO: these are the interial values from the Solo lower leg
                 link which is similar but not exactly the same to the FingerEdu
                 lower link. -->
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.009 -0.089"/>
      <mass value="0.03070"/>
      <inertia ixx="0.00012" ixy="0.00000" ixz="0.00000" iyy="0.00012" iyz="0.00000" izz="0.00000"/>
    </inertial>
  </link>
  <!-- fixed link for finger tip -->
  <link name="finger_tip_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.01"/>
      <inertia ixx="1.6666666666666668e-07" ixy="0" ixz="0" iyy="1.6666666666666668e-07" iyz="0" izz="1.6666666666666668e-07"/>
    </inertial>
  </link>
  <joint name="finger_lower_to_tip_joint" type="fixed">
    <parent link="finger_lower_link"/>
    <child link="finger_tip_link"/>
    <origin xyz="0 -0.008 -0.16"/>
  </joint>
  <!-- kinematics -->
  <joint name="finger_base_to_upper_joint" type="revolute">
    <parent link="finger_base_link"/>
    <child link="finger_upper_link"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="-1 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <joint name="finger_upper_to_middle_joint" type="revolute">
    <parent link="finger_upper_link"/>
    <child link="finger_middle_link"/>
    <limit effort="1000" lower="-1.5707963267948966" upper="1.5707963267948966" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.014 0"/>
  </joint>
  <joint name="finger_middle_to_lower_joint" type="revolute">
    <parent link="finger_middle_link"/>
    <child link="finger_lower_link"/>
    <limit effort="1000" lower="-3.141592653589793" upper="3.141592653589793" velocity="1000"/>
    <axis xyz="0 1 0"/>
    <origin rpy="0 0 0" xyz="0 -0.03745 -0.16"/>
  </joint>
  <!--
    Define the global base link and place the finger relative to it.
    -->
  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="1"/>
      <inertia ixx="4.166666666666667e-06" ixy="0" ixz="0" iyy="4.166666666666667e-06" iyz="0" izz="4.166666666666667e-06"/>
    </inertial>
  </link>
  <joint name="base_to_finger" type="fixed">
    <parent link="base_link"/>
    <child link="finger_base_link"/>
    <origin xyz="0 0 0.283"/>
  </joint>
</robot>

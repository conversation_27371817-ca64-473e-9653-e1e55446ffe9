<?xml version="1.0" ?>
<robot name="panda" xmlns:xacro="http://www.ros.org/wiki/xacro">
    <link name="panda_link0">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link0.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link0.obj"/>
            </geometry>
        </collision>
    </link>
    <link name="panda_link1">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link1.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link1.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint1" type="revolute">
        <origin rpy="0 0 0" xyz="0 0 0.333"/>
        <parent link="panda_link0"/>
        <child link="panda_link1"/>
        <axis xyz="0 0 1"/>
        <dynamics damping="10.0"/>
        <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
    </joint>
    <link name="panda_link2">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link2.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link2.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint2" type="revolute">
        <origin rpy="-1.57079632679 0 0" xyz="0 0 0"/>
        <parent link="panda_link1"/>
        <child link="panda_link2"/>
        <axis xyz="0 0 1"/>
        <dynamics damping="10.0"/>
        <limit effort="87" lower="-1.7628" upper="1.7628" velocity="2.1750"/>
    </joint>
    <link name="panda_link3">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link3.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link3.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint3" type="revolute">
        <origin rpy="1.57079632679 0 0" xyz="0 -0.316 0"/>
        <parent link="panda_link2"/>
        <child link="panda_link3"/>
        <axis xyz="0 0 1"/>
        <dynamics damping="10.0"/>
        <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
    </joint>
    <link name="panda_link4">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link4.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link4.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint4" type="revolute">
        <origin rpy="1.57079632679 0 0" xyz="0.0825 0 0"/>
        <parent link="panda_link3"/>
        <child link="panda_link4"/>
        <axis xyz="0 0 1"/>    
        <dynamics damping="10.0"/>
        <limit effort="87" lower="-3.0718" upper="-0.0698" velocity="2.1750"/>
    </joint>
    <link name="panda_link5">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link5.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link5.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint5" type="revolute">
        <origin rpy="-1.57079632679 0 0" xyz="-0.0825 0.384 0"/>
        <parent link="panda_link4"/>
        <child link="panda_link5"/>
        <axis xyz="0 0 1"/>
        <dynamics damping="10.0"/>
        <limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>
    </joint>
    <link name="panda_link6">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link6.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link6.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint6" type="revolute">
        <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
        <parent link="panda_link5"/>
        <child link="panda_link6"/>
        <axis xyz="0 0 1"/>
        <dynamics damping="10.0"/>
        <limit effort="12" lower="-0.0175" upper="3.7525" velocity="2.6100"/>
    </joint>
    <link name="panda_link7">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/link7.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/link7.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_joint7" type="revolute">
        <origin rpy="1.57079632679 0 0" xyz="0.088 0 0"/>
        <parent link="panda_link6"/>
        <child link="panda_link7"/>
        <axis xyz="0 0 1"/>
        <!--<dynamics damping="10.0"/>-->
        <!--<limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>-->
    </joint>
    <!--
    <link name="panda_link8"/>
    <joint name="panda_joint8" type="fixed">
        <origin rpy="0 0 0" xyz="0 0 0.107"/>
        <parent link="panda_link7"/>
        <child link="panda_link8"/>
        <axis xyz="0 0 0"/>
    </joint>
    -->
    <joint name="panda_hand_joint" type="fixed">
        <parent link="panda_link7"/>
        <child link="panda_hand"/>
        <origin rpy="0 0 -0.785398163397" xyz="0 0 0.107"/>
    </joint>
    <link name="panda_hand">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/hand.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/collision/hand.obj"/>
            </geometry>
        </collision>
    </link>
    <joint name="panda_finger_joint1" type="prismatic">
        <parent link="panda_hand"/>
        <child link="panda_leftfinger"/>
        <origin rpy="0 0 0" xyz="0 0 0.0584"/>
        <axis xyz="0 1 0"/>
        <dynamics damping="10.0"/>
        <limit effort="200" lower="0.0" upper="0.04" velocity="0.2"/>
    </joint>
    <joint name="panda_finger_joint2" type="prismatic">
        <parent link="panda_hand"/>
        <child link="panda_rightfinger"/>
        <origin rpy="0 0 0" xyz="0 0 0.0584"/>
        <axis xyz="0 -1 0"/>
        <dynamics damping="10.0"/>
        <limit effort="200" lower="0.0" upper="0.04" velocity="0.2"/>
    </joint>
    <link name="panda_leftfinger">
        <visual>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/finger.dae"/>
            </geometry>
        </visual>
        <collision>
            <geometry>
                <mesh filename="../mesh/factory_franka_table/factory_franka_finger_coll_subdiv_6x.obj"/>
            </geometry>
        </collision>
    </link>
    <link name="panda_rightfinger">
        <visual>
            <origin rpy="0 0 3.14159265359" xyz="0 0 0"/>
            <geometry>
                <mesh filename="../../urdf/franka_description/meshes/visual/finger.dae"/>
            </geometry>
        </visual>
        <collision>
            <origin rpy="0 0 3.14159265359" xyz="0 0 0"/>
            <geometry>
                <mesh filename="../mesh/factory_franka_table/factory_franka_finger_coll_subdiv_6x.obj"/>
            </geometry>
        </collision>
    </link>

    <!--Used for calculating pose and Jacobian.
    Joint connects hand to dummy link located at midpoint between bottoms of fingertips (when centered).-->
    <joint name="panda_fingertip_centered_joint" type="fixed">
        <parent link="panda_hand"/>
        <child link="panda_fingertip_centered"/>
        <!--0.0584 (franka_hand_length) + 0.04486700 (franka_finger_length) + 0.01760800 * 0.5 (franka_fingerpad_length * 0.5) = 0.112071-->
        <origin rpy="0 0 0" xyz="0 0 0.112071"/>
    </joint>
    <link name="panda_fingertip_centered"/>

</robot>

<mujoco model="BalanceBot">
  <compiler angle="degree" coordinate="local" inertiafromgeom="true" />
  <worldbody>
    <body name="tray" pos="0 0 0.559117">
      <joint name="root_joint" type="free" />
      <geom density="100" pos="0 0 0" size="0.5 0.01" type="cylinder" />
      <body name="upper_leg0" pos="0.272721 0 -0.157279" quat="0.382683 0 -0.92388 0">
        <geom density="1000" size="0.02 0.18" type="capsule" />
        <joint axis="0 1 0" limited="true" name="upper_leg_joint0" pos="0 0 -0.18" range="-45 45" type="hinge" />
        <body name="lower_leg0" pos="-0.18 0 0.18" quat="0.707107 0 -0.707107 0">
          <geom density="1000" size="0.02 0.18" type="capsule" />
          <joint axis="0 1 0" limited="true" name="lower_leg_joint0" pos="0 0 -0.18" range="-70 90" type="hinge" />
        </body>
      </body>
      <body name="upper_leg1" pos="-0.13636 0.236183 -0.157279" quat="0.191342 0.800103 -0.46194 0.331414">
        <geom density="1000" size="0.02 0.18" type="capsule" />
        <joint axis="0 1 0" limited="true" name="upper_leg_joint1" pos="0 0 -0.18" range="-45 45" type="hinge" />
        <body name="lower_leg1" pos="-0.18 0 0.18" quat="0.707107 0 -0.707107 0">
          <geom density="1000" size="0.02 0.18" type="capsule" />
          <joint axis="0 1 0" limited="true" name="lower_leg_joint1" pos="0 0 -0.18" range="-70 90" type="hinge" />
        </body>
      </body>
      <body name="upper_leg2" pos="-0.13636 -0.236183 -0.157279" quat="-0.191342 0.800103 0.46194 0.331414">
        <geom density="1000" size="0.02 0.18" type="capsule" />
        <joint axis="0 1 0" limited="true" name="upper_leg_joint2" pos="0 0 -0.18" range="-45 45" type="hinge" />
        <body name="lower_leg2" pos="-0.18 0 0.18" quat="0.707107 0 -0.707107 0">
          <geom density="1000" size="0.02 0.18" type="capsule" />
          <joint axis="0 1 0" limited="true" name="lower_leg_joint2" pos="0 0 -0.18" range="-70 90" type="hinge" />
        </body>
      </body>
    </body>
  </worldbody>
</mujoco>

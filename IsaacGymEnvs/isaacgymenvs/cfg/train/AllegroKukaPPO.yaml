params:
  seed: ${...seed}

  algo:
    name: a2c_continuous

  model:
    name: continuous_a2c_logstd

  network:
    name: actor_critic
    separate: False

    space:
      continuous:
        mu_activation: None
        sigma_activation: None
        mu_init:
          name: default
        sigma_init:
          name: const_initializer
          val: 0
        fixed_sigma: True

    mlp:
      units: [1024, 1024, 512, 512]
      activation: elu
      d2rl: False
      initializer:
        name: default
      regularizer:
        name: None

  load_checkpoint: ${if:${...checkpoint},True,False} # flag which sets whether to load the checkpoint
  load_path: ${...checkpoint} # path to the checkpoint to load

  config:
    name: ${resolve_default:AllegroKukaPPO,${....experiment}}
#    full_experiment_name: ${.name}
    env_name: rlgpu
    multi_gpu: ${....multi_gpu}
    ppo: True
    mixed_precision: True
    normalize_input: True
    normalize_value: True
    normalize_advantage: True
    reward_shaper:
      scale_value: 0.01

    num_actors: ${....task.env.numEnvs}   
    gamma: 0.99
    tau: 0.95
    learning_rate: 1e-4
    lr_schedule: adaptive
    schedule_type: standard
    kl_threshold: 0.016
    score_to_win: 1000000
    max_epochs: 100000
    max_frames: 10_000_000_000
    save_best_after: 100
    save_frequency: 5000
    print_stats: True
    grad_norm: 1.0
    entropy_coef: 0.0
    truncate_grads: True
    e_clip: 0.1
    minibatch_size: 32768
    mini_epochs: 4
    critic_coef: 4.0
    clip_value: True
    horizon_length: 16
    seq_length: 16

    # SampleFactory currently gives better results without bounds loss but I don't think this loss matters too much
    # bounds_loss_coef: 0.0
    bounds_loss_coef: 0.0001

    # optimize summaries to prevent tf.event files from growing to gigabytes
    defer_summaries_sec: ${if:${....pbt},240,5}
    summaries_interval_sec_min: ${if:${....pbt},60,5}
    summaries_interval_sec_max: 300

    player:
      #render: True
      deterministic: False  # be careful there's a typo in older versions of rl_games in this parameter name ("determenistic")
      games_num: 100000
      print_stats: False

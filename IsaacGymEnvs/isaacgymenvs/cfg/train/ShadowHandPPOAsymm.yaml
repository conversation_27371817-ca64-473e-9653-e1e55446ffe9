params:
  seed: ${...seed}

  algo:
    name: a2c_continuous

  model:
    name: continuous_a2c_logstd

  network:
    name: actor_critic
    separate: False
    space:
      continuous:
        mu_activation: None
        sigma_activation: None
        mu_init:
          name: default
        sigma_init:
          name: const_initializer
          val: 0
        fixed_sigma: True

    mlp:
      units: [400, 400, 200, 100]
      activation: elu
      d2rl: False

      initializer:
        name: default
      regularizer:
        name: None

  load_checkpoint: ${if:${...checkpoint},True,False} # flag which sets whether to load the checkpoint
  load_path: ${...checkpoint} # path to the checkpoint to load

  config:
    name: ${resolve_default:ShadowHandAsymm,${....experiment}}
    full_experiment_name: ${.name}
    env_name: rlgpu
    multi_gpu: ${....multi_gpu}
    ppo: True
    mixed_precision: False
    normalize_input: True
    normalize_value: True
    reward_shaper:
      scale_value: 0.01
    normalize_advantage: True
    num_actors: ${....task.env.numEnvs}
    gamma: 0.99
    tau: 0.95
    learning_rate: 5e-4
    lr_schedule: adaptive
    schedule_type: standard
    kl_threshold: 0.016
    score_to_win: 100000
    max_epochs: ${resolve_default:10000,${....max_iterations}}
    save_best_after: 500
    save_frequency: 200
    print_stats: True
    grad_norm: 1.0
    entropy_coef: 0.0
    truncate_grads: True
    e_clip: 0.2
    horizon_length: 8
    minibatch_size: 16384
    mini_epochs: 8
    critic_coef: 4
    clip_value: True
    seq_len: 4
    bounds_loss_coef: 0.0001

    central_value_config:
      minibatch_size: 16384
      mini_epochs: 8
      learning_rate: 5e-4
      lr_schedule: adaptive
      schedule_type: standard
      kl_threshold: 0.016
      clip_value: True
      normalize_input: True
      truncate_grads: True

      network:
        name: actor_critic
        central_value: True
        mlp:
          units: [512, 512, 256, 128]
          activation: elu
          d2rl: False
          initializer:
            name: default
          regularizer:
            name: None

    player:
      #render: True
      deterministic: True
      games_num: 1000000
      print_stats: False

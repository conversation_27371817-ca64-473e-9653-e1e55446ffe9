params:
  seed: ${...seed}

  algo:
    name: amp_continuous

  model:
    name: continuous_amp

  network:
    name: amp
    separate: True

    space:
      continuous:
        mu_activation: None
        sigma_activation: None
        mu_init:
          name: default
        sigma_init:
          name: const_initializer
          val: -2.9
        fixed_sigma: True
        learn_sigma: False

    mlp:
      units: [1024, 512]
      activation: relu
      d2rl: False

      initializer:
        name: default
      regularizer:
        name: None

    disc:
      units: [1024, 512]
      activation: relu

      initializer:
        name: default

  load_checkpoint: ${if:${...checkpoint},True,False} # flag which sets whether to load the checkpoint
  load_path: ${...checkpoint} # path to the checkpoint to load

  config:
    name: ${resolve_default:HumanoidAMP,${....experiment}}
    full_experiment_name: ${.name}
    env_name: rlgpu
    ppo: True
    multi_gpu: ${....multi_gpu}
    mixed_precision: False
    normalize_input: True
    normalize_value: True
    value_bootstrap: True
    num_actors: ${....task.env.numEnvs}
    reward_shaper:
      scale_value: 1
    normalize_advantage: True
    gamma: 0.99
    tau: 0.95
    learning_rate: 5e-5
    lr_schedule: constant
    kl_threshold: 0.008
    score_to_win: 20000
    max_epochs: ${resolve_default:5000,${....max_iterations}}
    save_best_after: 100
    save_frequency: 50
    print_stats: True
    grad_norm: 1.0
    entropy_coef: 0.0
    truncate_grads: False
    e_clip: 0.2
    horizon_length: 16
    minibatch_size: 32768
    mini_epochs: 6
    critic_coef: 5
    clip_value: False
    seq_len: 4
    bounds_loss_coef: 10
    amp_obs_demo_buffer_size: 200000
    amp_replay_buffer_size: 1000000
    amp_replay_keep_prob: 0.01
    amp_batch_size: 512
    amp_minibatch_size: 4096
    disc_coef: 5
    disc_logit_reg: 0.05
    disc_grad_penalty: 0.2
    disc_reward_scale: 2
    disc_weight_decay: 0.0001
    normalize_amp_input: True

    task_reward_w: 0.0
    disc_reward_w: 1.0

defaults:
  - AllegroKukaLSTMPPO
  - _self_

# TODO: try bigger network for two hands?
params:
  network:
    mlp:
      units: [768, 512, 256]
      activation: elu
      d2rl: False
      initializer:
        name: default
      regularizer:
        name: None
    rnn:
      name: lstm
      units: 768
      layers: 1
      before_mlp: True
      layer_norm: True

  config:
    name: ${resolve_default:AllegroKukaTwoArmsLSTMPPO,${....experiment}}
    minibatch_size: 32768
    mini_epochs: 2
asymmetric_obs: true
params:
  seed: ${...seed}
  algo:
    name: a2c_continuous
  model:
    name: continuous_a2c_logstd
  network:
    name: actor_critic
    separate: false
    space:
      continuous:
        mu_activation: None
        sigma_activation: None
        mu_init:
          name: default
        sigma_init:
          name: const_initializer
          val: 0
        fixed_sigma: true
    mlp:
      units: [256, 256, 128, 128]
      activation: elu
      d2rl: False
      initializer:
        name: default
      regularizer:
        name: None
  load_checkpoint: ${if:${...checkpoint},True,False} # flag which sets whether to load the checkpoint
  load_path: ${...checkpoint} # path to the checkpoint to load
  config:
    name: ${resolve_default:Trifinger,${....experiment}}
    full_experiment_name: ${.name}
    env_name: rlgpu
    multi_gpu: ${....multi_gpu}
    ppo: true
    mixed_precision: false
    normalize_input: true
    normalize_value: true
    reward_shaper:
      scale_value: 0.01
    normalize_advantage: true
    gamma: 0.99
    tau: 0.95
    learning_rate: 0.0003
    lr_schedule: constant
    use_experimental_cv: true
    schedule_type: standard
    kl_threshold: 0.016
    score_to_win: 500000
    max_epochs: ${resolve_default:20000,${....max_iterations}}
    save_best_after: 100
    save_frequency: 100
    print_stats: true
    grad_norm: 1.0
    entropy_coef: 0.0
    truncate_grads: true
    e_clip: 0.2
    horizon_length: 8
    minibatch_size: ${.num_actors}
    mini_epochs: 4
    critic_coef: 4
    clip_value: true
    seq_len: 4
    bounds_loss_coef: 0.0001
    central_value_config:
      minibatch_size: ${..num_actors}
      mini_epochs: ${..mini_epochs}
      learning_rate: 0.0005
      lr_schedule: linear
      schedule_type: standard
      kl_threshold: 0.016
      clip_value: true
      normalize_input: true
      truncate_grads: true
      network:
        name: actor_critic
        central_value: true
        mlp:
          units: [512, 512, 256, 128]
          activation: elu
          d2rl: false
          initializer:
            name: default
          regularizer:
            name: None
    player:
      deterministic: true
      games_num: 1000000
      print_stats: false
    num_actors: ${....task.env.numEnvs}
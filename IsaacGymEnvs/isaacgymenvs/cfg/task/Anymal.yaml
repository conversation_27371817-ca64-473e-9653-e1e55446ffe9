# used to create the object
name: Anymal

physics_engine: ${..physics_engine}

env:
  numEnvs: ${resolve_default:4096,${...num_envs}}
  envSpacing: 4.  # [m]

  clipObservations: 5.0
  clipActions: 1.0

  plane:
    staticFriction: 1.0  # [-]
    dynamicFriction: 1.0  # [-]
    restitution: 0.        # [-]

  baseInitState:
    pos: [0.0, 0.0, 0.62] # x,y,z [m]
    rot: [0.0, 0.0, 0.0, 1.0] # x,y,z,w [quat]
    vLinear: [0.0, 0.0, 0.0]  # x,y,z [m/s]
    vAngular: [0.0, 0.0, 0.0]  # x,y,z [rad/s]

  randomCommandVelocityRanges:
    linear_x: [-2., 2.] # min max [m/s]
    linear_y: [-1., 1.]   # min max [m/s]
    yaw: [-1., 1.]          # min max [rad/s]

  control:
    # PD Drive parameters:
    stiffness: 85.0  # [N*m/rad]
    damping: 2.0     # [N*m*s/rad]
    actionScale: 0.5
    controlFrequencyInv: 1 # 60 Hz

  defaultJointAngles:  # = target angles when action = 0.0
    LF_HAA: 0.03    # [rad]
    LH_HAA: 0.03    # [rad]
    RF_HAA: -0.03   # [rad]
    RH_HAA: -0.03   # [rad]

    LF_HFE: 0.4     # [rad]
    LH_HFE: -0.4    # [rad]
    RF_HFE: 0.4     # [rad]
    RH_HFE: -0.4    # [rad]

    LF_KFE: -0.8    # [rad]
    LH_KFE: 0.8     # [rad]
    RF_KFE: -0.8    # [rad]
    RH_KFE: 0.8     # [rad]

  urdfAsset:
    collapseFixedJoints: True
    fixBaseLink: False
    defaultDofDriveMode: 4 # see GymDofDriveModeFlags (0 is none, 1 is pos tgt, 2 is vel tgt, 4 effort)

  learn:
    # rewards
    linearVelocityXYRewardScale: 1.0
    angularVelocityZRewardScale: 0.5
    torqueRewardScale: -0.000025 

    # normalization
    linearVelocityScale: 2.0
    angularVelocityScale: 0.25
    dofPositionScale: 1.0
    dofVelocityScale: 0.05

    # episode length in seconds
    episodeLength_s: 50

  # viewer cam:
  viewer:
    refEnv: 0
    pos: [0, 0, 4]  # [m]
    lookat: [1., 1, 3.3]  # [m]

  # set to True if you use camera sensors in the environment
  enableCameraSensors: False

sim:
  dt: 0.02
  substeps: 2
  up_axis: "z"
  use_gpu_pipeline: ${eq:${...pipeline},"gpu"}
  gravity: [0.0, 0.0, -9.81]
  physx:
    num_threads: ${....num_threads}
    solver_type: ${....solver_type}
    use_gpu: ${contains:"cuda",${....sim_device}} # set to False to run on CPU
    num_position_iterations: 4
    num_velocity_iterations: 1
    contact_offset: 0.02
    rest_offset: 0.0
    bounce_threshold_velocity: 0.2
    max_depenetration_velocity: 100.0
    default_buffer_size_multiplier: 5.0
    max_gpu_contact_pairs: 8388608 # 8*1024*1024
    num_subscenes: ${....num_subscenes}
    contact_collection: 1 # 0: CC_NEVER (don't collect contact info), 1: CC_LAST_SUBSTEP (collect only contacts on last substep), 2: CC_ALL_SUBSTEPS (broken - do not use!)

task:
  randomize: False
  randomization_params:
    frequency: 600   # Define how many environment steps between generating new randomizations
    observations:
      range: [0, .002] # range for the white noise
      operation: "additive"
      distribution: "gaussian"
    actions:
      range: [0., .02]
      operation: "additive"
      distribution: "gaussian"
    sim_params: 
      gravity:
        range: [0, 0.4]
        operation: "additive"
        distribution: "gaussian"
        schedule: "linear"  # "linear" will linearly interpolate between no rand and max rand
        schedule_steps: 3000
    actor_params:
      anymal:
        color: True
        rigid_body_properties:
          mass: 
            range: [0.5, 1.5]
            operation: "scaling"
            distribution: "uniform"
            setup_only: True # Property will only be randomized once before simulation is started. See Domain Randomization Documentation for more info.
            schedule: "linear"  # "linear" will linearly interpolate between no rand and max rand
            schedule_steps: 3000
        rigid_shape_properties:
          friction:
            num_buckets: 500
            range: [0.7, 1.3]
            operation: "scaling"
            distribution: "uniform"
            schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            schedule_steps: 3000
          restitution:
            range: [0., 0.7]
            operation: "scaling"
            distribution: "uniform"
            schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            schedule_steps: 3000
        dof_properties:
          damping: 
            range: [0.5, 1.5]
            operation: "scaling"
            distribution: "uniform"
            schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            schedule_steps: 3000
          stiffness: 
            range: [0.5, 1.5]
            operation: "scaling"
            distribution: "uniform"
            schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            schedule_steps: 3000
          lower:
            range: [0, 0.01]
            operation: "additive"
            distribution: "gaussian"
            schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            schedule_steps: 3000
          upper:
            range: [0, 0.01]
            operation: "additive"
            distribution: "gaussian"
            schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            schedule_steps: 3000

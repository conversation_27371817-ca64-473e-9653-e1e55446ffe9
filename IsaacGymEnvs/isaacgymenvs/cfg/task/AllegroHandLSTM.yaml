# used to create the object
name: AllegroHand

physics_engine: ${..physics_engine}

# if given, will override the device setting in gym.
env: 
  numEnvs: ${resolve_default:16384,${...num_envs}}
  envSpacing: 0.75
  episodeLength: 320 # Not used, but would be 8 sec if resetTime is not set
  resetTime: 16 # Max time till reset, in seconds, if a goal wasn't achieved. Will overwrite the episodeLength if is > 0.
  enableDebugVis: False
  aggregateMode: 1

  clipActions: 1.0

  stiffnessScale: 1.0
  forceLimitScale: 1.0

  useRelativeControl: False
  dofSpeedScale: 20.0

  use_capped_dof_control: False 
  max_dof_radians_per_second: 3.1415

  # This is to generate correct random goals 
  apply_random_quat: False 

  actionsMovingAverage: 
    range: [0.15, 0.35]
    schedule_steps: 1000_000
    #schedule_steps: 300_000
    schedule_freq: 500 # schedule every 500 steps for stability

  controlFrequencyInv: 2 #2 # 30 Hz #3 # 20 Hz

  cubeObsDelayProb: 0.3
  maxObjectSkipObs: 2

  # Action Delay related 
  # right now the schedule steps are so big that
  # it virtually never changes the latency
  # our best seed came out of this config file 
  # so for now keeping it as it is, will look into it in future
  actionDelayProbMax: 0.3
  actionLatencyMax: 15
  actionLatencyScheduledSteps: 10_000_000

  startPositionNoise: 0.01
  startRotationNoise: 0.0

  resetPositionNoise: 0.03
  resetPositionNoiseZ: 0.01
  resetRotationNoise: 0.0
  resetDofPosRandomInterval: 0.5
  resetDofVelRandomInterval: 0.0

  startObjectPoseDY: -0.19
  startObjectPoseDZ: 0.06

  # Random forces applied to the object
  forceScale: 2.0
  forceProbRange: [0.001, 0.1]
  forceDecay: 0.99
  forceDecayInterval: 0.08

  # Random Adversarial Perturbations
  random_network_adversary:
    enable: True
    prob: 0.15
    weight_sample_freq: 1000 # steps 

  # Provide random cube observations to model pose jumps in the real
  random_cube_observation:
    enable: True 
    prob: 0.3

  # reward -> dictionary
  distRewardScale: -10.0
  rotRewardScale: 1.0
  rotEps: 0.1
  actionPenaltyScale: -0.0001
  actionDeltaPenaltyScale: -0.01
  reachGoalBonus: 250
  fallDistance: 0.24
  fallPenalty: 0.0

  objectType: "block" # can be block, egg or pen
  observationType: "no_vel" #"full_state" # can be "no_vel", "full_state"
  asymmetric_observations: True
  successTolerance: 0.4
  printNumSuccesses: False
  maxConsecutiveSuccesses: 50

  asset:
    assetFileName: "urdf/kuka_allegro_description/allegro.urdf"
    # assetFileNameBlock: "urdf/objects/cube_multicolor_dextreme.urdf"
    # assetFileNameBlock: "urdf/objects/cube_multicolor_allegro.urdf"
    assetFileNameEgg: "mjcf/open_ai_assets/hand/egg.xml"
    assetFileNamePen: "mjcf/open_ai_assets/hand/pen.xml"

  # set to True if you use camera sensors in the environment
  enableCameraSensors: False

task:
  randomize: True
  randomization_params:
    frequency: 720   # Define how many simulation steps between generating new randomizations
    observations:
    # There is a hidden variable `apply_white_noise_prob` which is set to 0.5
    # so that the observation noise is added only 50% of the time.
      dof_pos:
        range: [0, .005] # range for the white noise
        range_correlated: [0, .01 ] # range for correlated noise, refreshed with freq `frequency`
        operation: "additive"
        distribution: "gaussian"
        # schedule: "linear"   # "constant" is to turn on noise after `schedule_steps` num steps
        # schedule_steps: 40000
      object_pose_cam:
        range: [0, .005] # range for the white noise
        range_correlated: [0, .01 ] # range for correlated noise, refreshed with freq `frequency`
        operation: "additive"
        distribution: "gaussian"
        # schedule: "linear"   # "constant" is to turn on noise after `schedule_steps` num steps
        # schedule_steps: 40000
      goal_pose:
        range: [0, .005] # range for the white noise
        range_correlated: [0, .01 ] # range for correlated noise, refreshed with freq `frequency`
        operation: "additive"
        distribution: "gaussian"
        # schedule: "linear"   # "constant" is to turn on noise after `schedule_steps` num steps
        # schedule_steps: 40000
      goal_relative_rot_cam:
        range: [0, .005] # range for the white noise
        range_correlated: [0, .01 ] # range for correlated noise, refreshed with freq `frequency`
        operation: "additive"
        distribution: "gaussian"
        # schedule: "linear"   # "constant" is to turn on noise after `schedule_steps` num steps
        # schedule_steps: 40000
      last_actions:
        range: [0, .005] # range for the white noise
        range_correlated: [0, .01 ] # range for correlated noise, refreshed with freq `frequency`
        operation: "additive"
        distribution: "gaussian"
        # schedule: "linear"   # "constant" is to turn on noise after `schedule_steps` num steps
        # schedule_steps: 40000
    actions:
      range: [0., .05]
      range_correlated: [0, .02] # range for correlated noise, refreshed with freq `frequency`
      operation: "additive"
      distribution: "gaussian"
      # schedule: "linear"  # "linear" will linearly interpolate between no rand and max rand
      # schedule_steps: 40000
    sim_params:
      gravity:
        range: [0, 0.5]
        operation: "additive"
        distribution: "gaussian"
        # schedule: "linear"  # "linear" will linearly interpolate between no rand and max rand
        # schedule_steps: 40000
        #rest_offset:
        #range: [0, 0.007]
        #operation: "additive"
        #distribution: "uniform"
        #schedule: "linear"
        #schedule_steps: 6000
    actor_params:
      hand:
        # scale:
        #   range: [0.95, 1.05]
        #   operation: "scaling"
        #   distribution: "uniform"
        #   setup_only: True         

        color: True
        dof_properties:
          damping: 
            range: [0.3, 3.0]
            operation: "scaling"
            distribution: "loguniform"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
          stiffness:
            range: [0.75, 1.5]
            operation: "scaling"
            distribution: "loguniform"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
          lower:
            range: [0, 0.01]
            operation: "additive"
            distribution: "gaussian"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
          upper:
            range: [0, 0.01]
            operation: "additive"
            distribution: "gaussian"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
        rigid_body_properties:
          mass:
            range: [0.5, 2.0]
            operation: "scaling"
            distribution: "uniform"
            setup_only: True # Property will only be randomized once before simulation is started. See Domain Randomization Documentation for more info.
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
        rigid_shape_properties:
          friction:
            num_buckets: 250
            range: [0.2, 1.2] #[0.7, 1.3]
            operation: "scaling"
            distribution: "uniform"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
          restitution:
            num_buckets: 100
            range: [0.0, 0.4]
            operation: "additive"
            distribution: "uniform"

      object:
        scale:
          range: [0.95, 1.05]
          operation: "scaling"
          distribution: "uniform"
          setup_only: True # Property will only be randomized once before simulation is started. See Domain Randomization Documentation for more info.
          # schedule: "linear"  # "linear" will scale the current random sample by ``min(current num steps, schedule_steps) / schedule_steps`
          # schedule_steps: 30000
        rigid_body_properties:
          mass:
            range: [0.5, 1.5]
            operation: "scaling"
            distribution: "uniform"
            setup_only: True # Property will only be randomized once before simulation is started. See Domain Randomization Documentation for more info.
            # schedule: "linear"  # "linear" will scale the current random sample by ``min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
        rigid_shape_properties:
          friction:
            num_buckets: 250
            range: [0.2, 1.2] #[0.7, 1.3]
            operation: "scaling"
            distribution: "uniform"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
          restitution:
            num_buckets: 100
            range: [0.0, 0.4]
            operation: "additive"
            distribution: "uniform"

sim:
  dt: 0.01667 # 1/60
  substeps: 2
  up_axis: "z"
  use_gpu_pipeline: ${eq:${...pipeline},"gpu"}
  gravity: [0.0, 0.0, -9.81]
  physx:
    num_threads: ${....num_threads}
    solver_type: ${....solver_type}
    use_gpu: ${contains:"cuda",${....sim_device}} # set to False to run on CPU
    num_position_iterations: 8
    num_velocity_iterations: 0
    max_gpu_contact_pairs: 8388608 # 8*1024*1024
    num_subscenes: ${....num_subscenes}
    contact_offset: 0.005
    rest_offset: 0.0
    bounce_threshold_velocity: 0.2
    max_depenetration_velocity: 1.0 #1000.0
    default_buffer_size_multiplier: 75.0
    contact_collection: 0 # 0: CC_NEVER (don't collect contact info), 1: CC_LAST_SUBSTEP (collect only contacts on last substep), 2: CC_ALL_SUBSTEPS ((broken - do not use!)

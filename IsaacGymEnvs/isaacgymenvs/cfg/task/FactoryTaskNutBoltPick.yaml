# See schema in factory_schema_config_task.py for descriptions of common parameters.

defaults:
    - FactoryBase
    - _self_
    # - /factory_schema_config_task

name: FactoryTaskNutBoltPick
physics_engine: ${..physics_engine}

sim:
    disable_gravity: False

env:
    numEnvs: ${resolve_default:128,${...num_envs}}
    numObservations: 20
    numActions: 12

    close_and_lift: True  # close gripper and lift after last step of episode
    num_gripper_move_sim_steps: 20  # number of timesteps to reserve for moving gripper before first step of episode
    num_gripper_close_sim_steps: 25  # number of timesteps to reserve for closing gripper after last step of episode
    num_gripper_lift_sim_steps: 25  # number of timesteps to reserve for lift after last step of episode

randomize:
    franka_arm_initial_dof_pos: [0.3413, -0.8011, -0.0670, -1.8299,  0.0266,  1.0185,  1.0927]
    fingertip_midpoint_pos_initial: [0.0, -0.2, 0.2]  # initial position of hand above table
    fingertip_midpoint_pos_noise: [0.2, 0.2, 0.1]  # noise on hand position
    fingertip_midpoint_rot_initial: [3.1416, 0, 3.1416]  # initial rotation of fingertips (Euler)
    fingertip_midpoint_rot_noise: [0.3, 0.3, 1]  # noise on rotation
    nut_pos_xy_initial: [0.0, -0.3]  # initial XY position of nut on table
    nut_pos_xy_initial_noise: [0.1, 0.1]  # noise on nut position
    bolt_pos_xy_initial: [0.0, 0.0]  # initial position of bolt on table
    bolt_pos_xy_noise: [0.1, 0.1]  # noise on bolt position

rl:
    pos_action_scale: [0.1, 0.1, 0.1]
    rot_action_scale: [0.1, 0.1, 0.1]
    force_action_scale: [1.0, 1.0, 1.0]
    torque_action_scale: [1.0, 1.0, 1.0]

    clamp_rot: True
    clamp_rot_thresh: 1.0e-6

    num_keypoints: 4  # number of keypoints used in reward
    keypoint_scale: 0.5  # length of line of keypoints

    keypoint_reward_scale: 1.0  # scale on keypoint-based reward
    action_penalty_scale: 0.0  # scale on action penalty

    max_episode_length: 100

    success_bonus: 0.0  # bonus if nut has been lifted

ctrl:
    ctrl_type: joint_space_id  # {gym_default,
                               #  joint_space_ik, joint_space_id, 
                               #  task_space_impedance, operational_space_motion, 
                               #  open_loop_force, closed_loop_force,
                               #  hybrid_force_motion}
    all:
        jacobian_type: geometric
        gripper_prop_gains: [50, 50]
        gripper_deriv_gains: [2, 2]
    gym_default:
        ik_method: dls
        joint_prop_gains: [40, 40, 40, 40, 40, 40, 40]
        joint_deriv_gains: [8, 8, 8, 8, 8, 8, 8]
        gripper_prop_gains: [500, 500]
        gripper_deriv_gains: [20, 20]
    joint_space_ik:
        ik_method: dls
        joint_prop_gains: [1, 1, 1, 1, 1, 1, 1]
        joint_deriv_gains: [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]
    joint_space_id:
        ik_method: dls
        joint_prop_gains: [40, 40, 40, 40, 40, 40, 40]
        joint_deriv_gains: [8, 8, 8, 8, 8, 8, 8]
    task_space_impedance:
        motion_ctrl_axes: [1, 1, 1, 1, 1, 1]
        task_prop_gains: [40, 40, 40, 40, 40, 40]
        task_deriv_gains: [8, 8, 8, 8, 8, 8]
    operational_space_motion:
        motion_ctrl_axes: [1, 1, 1, 1, 1, 1]
        task_prop_gains: [1, 1, 1, 1, 1, 1]
        task_deriv_gains: [1, 1, 1, 1, 1, 1]
    open_loop_force:
        force_ctrl_axes: [0, 0, 1, 0, 0, 0]
    closed_loop_force:
        force_ctrl_axes: [0, 0, 1, 0, 0, 0]
        wrench_prop_gains: [0.1, 0.1, 0.1, 0.1, 0.1, 0.1]
    hybrid_force_motion:
        motion_ctrl_axes: [1, 1, 0, 1, 1, 1]
        task_prop_gains: [40, 40, 40, 40, 40, 40]
        task_deriv_gains: [8, 8, 8, 8, 8, 8]
        force_ctrl_axes: [0, 0, 1, 0, 0, 0]
        wrench_prop_gains: [0.1, 0.1, 0.1, 0.1, 0.1, 0.1]
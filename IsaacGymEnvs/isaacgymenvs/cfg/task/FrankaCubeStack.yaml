# used to create the object
name: FrankaCubeStack

physics_engine: ${..physics_engine}

# if given, will override the device setting in gym. 
env:
  numEnvs: ${resolve_default:8192,${...num_envs}}
  envSpacing: 1.5
  episodeLength: 300
  enableDebugVis: False

  clipObservations: 5.0
  clipActions: 1.0

  startPositionNoise: 0.25
  startRotationNoise: 0.785
  frankaPositionNoise: 0.0
  frankaRotationNoise: 0.0
  frankaDofNoise: 0.25

  aggregateMode: 3

  actionScale: 1.0
  distRewardScale: 0.1
  liftRewardScale: 1.5
  alignRewardScale: 2.0
  stackRewardScale: 16.0

  controlType: osc  # options are {joint_tor, osc}

  asset:
    assetRoot: "../../assets"
    assetFileNameFranka: "urdf/franka_description/robots/franka_panda_gripper.urdf"

  # set to True if you use camera sensors in the environment
  enableCameraSensors: False

sim:
  dt: 0.01667 # 1/60
  substeps: 2
  up_axis: "z"
  use_gpu_pipeline: ${eq:${...pipeline},"gpu"}
  gravity: [0.0, 0.0, -9.81]
  physx:
    num_threads: ${....num_threads}
    solver_type: ${....solver_type}
    use_gpu: ${contains:"cuda",${....sim_device}} # set to False to run on CPU
    num_position_iterations: 8
    num_velocity_iterations: 1
    contact_offset: 0.005
    rest_offset: 0.0
    bounce_threshold_velocity: 0.2
    max_depenetration_velocity: 1000.0
    default_buffer_size_multiplier: 5.0
    max_gpu_contact_pairs: 1048576 # 1024*1024
    num_subscenes: ${....num_subscenes}
    contact_collection: 0 # 0: CC_NEVER (don't collect contact info), 1: CC_LAST_SUBSTEP (collect only contacts on last substep), 2: CC_ALL_SUBSTEPS (broken - do not use!)

task:
  randomize: False

name: Trifinger

physics_engine: ${..physics_engine}

env:
  aggregate_mode: True

  control_decimation: 1
  envSpacing: 1.0

  numEnvs: ${resolve_default:16384,${...num_envs}}

  episodeLength: 750

  clipObservations: 5.0
  clipActions: 1.0

  task_difficulty: 4
  enable_ft_sensors: false
  asymmetric_obs: true
  normalize_obs: true

  apply_safety_damping: true
  command_mode: torque
  normalize_action: true
  cube_obs_keypoints: true
  reset_distribution:
    object_initial_state:
      type: random
    robot_initial_state:
      dof_pos_stddev: 0.4
      dof_vel_stddev: 0.2
      type: default

  reward_terms:
    finger_move_penalty:
      activate: true
      weight: -0.5
    finger_reach_object_rate:
      activate: true
      norm_p: 2
      weight: -250
    object_dist:
      activate: false
      weight: 2000
    object_rot:
      activate: false
      weight: 2000
    keypoints_dist:
      activate: true
      weight: 2000
  termination_conditions:
    success:
      orientation_tolerance: 0.4
      position_tolerance: 0.02

  # set to True if you use camera sensors in the environment
  enableCameraSensors: False

sim:
  dt: 0.02
  substeps: 4
  up_axis: z
  use_gpu_pipeline: ${eq:${...pipeline},"gpu"}
  gravity:
  - 0.0
  - 0.0
  - -9.81
  physx:
    num_threads: ${....num_threads}
    solver_type: ${....solver_type}
    use_gpu: ${contains:"cuda",${....sim_device}} # set to False to run on CPU
    num_position_iterations: 8
    num_velocity_iterations: 0
    contact_offset: 0.002
    rest_offset: 0.0
    bounce_threshold_velocity: 0.5
    max_depenetration_velocity: 1000.0
    default_buffer_size_multiplier: 5.0
    max_gpu_contact_pairs: 8388608 # 8*1024*1024
    num_subscenes: ${....num_subscenes}
    contact_collection: 0 # 0: CC_NEVER (don't collect contact info), 1: CC_LAST_SUBSTEP (collect only contacts on last substep), 2: CC_ALL_SUBSTEPS (broken - do not use!)

task:
  randomize: True
  randomization_params:
    frequency: 750   # Define how many simulation steps between generating new randomizations
    observations:
      range: [0, .002] # range for the white noise
      range_correlated: [0, .000 ] # range for correlated noise, refreshed with freq `frequency`
      operation: "additive"
      distribution: "gaussian"
      # schedule: "linear"   # "constant" is to turn on noise after `schedule_steps` num steps
      # schedule_steps: 40000
    actions:
      range: [0., .02]
      range_correlated: [0, .01] # range for correlated noise, refreshed with freq `frequency`
      operation: "additive"
      distribution: "gaussian"
      # schedule: "linear"  # "linear" will linearly interpolate between no rand and max rand
      # schedule_steps: 40000
    sim_params:
      gravity:
        range: [0, 0.4]
        operation: "additive"
        distribution: "gaussian"
        # schedule: "linear"  # "linear" will linearly interpolate between no rand and max rand
        # schedule_steps: 40000
    actor_params:
      robot:
        color: True
        dof_properties:
          lower:
            range: [0, 0.01]
            operation: "additive"
            distribution: "gaussian"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
          upper:
            range: [0, 0.01]
            operation: "additive"
            distribution: "gaussian"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
      object:
        scale:
          range: [0.97, 1.03]
          operation: "scaling"
          distribution: "uniform"
          setup_only: True # Property will only be randomized once before simulation is started. See Domain Randomization Documentation for more info.
          # schedule: "linear"  # "linear" will scale the current random sample by ``min(current num steps, schedule_steps) / schedule_steps`
          # schedule_steps: 30000
        rigid_body_properties:
          mass:
            range: [0.7, 1.3]
            operation: "scaling"
            distribution: "uniform"
            setup_only: True # Property will only be randomized once before simulation is started. See Domain Randomization Documentation for more info.
            # schedule: "linear"  # "linear" will scale the current random sample by ``min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
        rigid_shape_properties:
          friction:
            num_buckets: 250
            range: [0.7, 1.3]
            operation: "scaling"
            distribution: "uniform"
            # schedule: "linear"  # "linear" will scale the current random sample by `min(current num steps, schedule_steps) / schedule_steps`
            # schedule_steps: 30000
      table:
        rigid_shape_properties:
          friction:
            num_buckets: 250
            range: [0.5, 1.5]
            operation: "scaling"
            distribution: "uniform"

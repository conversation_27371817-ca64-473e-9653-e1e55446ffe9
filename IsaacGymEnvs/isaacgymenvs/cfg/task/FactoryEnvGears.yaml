# See schema in factory_schema_config_env.py for descriptions of common parameters.

defaults:
    - FactoryBase
    - _self_
    - /factory_schema_config_env

sim:
    disable_franka_collisions: False

env:
    env_name: 'FactoryEnvGears'

    tight_or_loose: loose  # use assets with loose (maximal clearance) or tight (minimal clearance) shafts
    gears_lateral_offset: 0.1  # Y-axis offset of gears before initial reset to prevent initial interpenetration with base plate
    gears_density: 1000.0  # density of gears
    base_density: 2700.0  # density of base plate
    gears_friction: 0.3  # coefficient of friction associated with gears
    base_friction: 0.3  # coefficient of friction associated with base plate

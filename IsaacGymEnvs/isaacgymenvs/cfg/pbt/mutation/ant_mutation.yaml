task.env.headingWeight: "mutate_float"
task.env.upWeight: "mutate_float"

train.params.config.grad_norm: "mutate_float"
train.params.config.entropy_coef: "mutate_float"
train.params.config.critic_coef: "mutate_float"
train.params.config.bounds_loss_coef: "mutate_float"
train.params.config.kl_threshold: "mutate_float"

train.params.config.e_clip: "mutate_eps_clip"

train.params.config.mini_epochs: "mutate_mini_epochs"

train.params.config.gamma: "mutate_discount"
train.params.config.tau: "mutate_discount"
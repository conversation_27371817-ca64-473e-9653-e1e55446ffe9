# Agent ONNX Export

This directory contains scripts to export the student agent's `getAction` operation to ONNX format for deployment and inference.

## Overview

The ONNX export captures the complete inference pipeline of the student agent:

1. **Observation Normalization**: Uses running mean and variance statistics (ObsRMS)
2. **Neural Network Forward Pass**: ActorGaussian network with MLP architecture
3. **Action Generation**: Deterministic or stochastic action sampling
4. **Action Denormalization**: Convert from normalized [-1,1] to actual action bounds

## Files

- `export_agent_to_onnx.py`: Main export script
- `test_onnx_export.py`: Testing and comparison script
- `ONNX_EXPORT_README.md`: This documentation

## Requirements

```bash
pip install onnx onnxruntime
```

## Usage

### 1. Export Agent to ONNX

```bash
python export_agent_to_onnx.py --task_cfg_path tasks/robs3go_twohand.yaml --algo_cfg_path algos/student/go1_twohand.yaml --model_num 30000000 --output_path student_agent.onnx  --device_type cpu
```

**Parameters:**
- `--task_cfg_path`: Path to task configuration file
- `--algo_cfg_path`: Path to algorithm configuration file  
- `--model_num`: Model checkpoint number to load
- `--output_path`: Output ONNX file path (default: student_agent.onnx)
- `--device_type`: Device for export (cpu/gpu, default: cpu)
- `--gpu_idx`: GPU index if using GPU (default: 0)
- `--seed`: Random seed (default: 1)

### 2. Test and Compare ONNX Model

```bash
python test_onnx_export.py \
    --task_cfg_path configs/task/your_task.yaml \
    --algo_cfg_path configs/algo/student/your_config.yaml \
    --model_num 1000000 \
    --onnx_path student_agent.onnx \
    --num_tests 100 \
    --benchmark
```

**Parameters:**
- `--num_tests`: Number of random tests for comparison (default: 10)
- `--benchmark`: Run inference speed benchmark
- Other parameters same as export script

## ONNX Model Details

### Input
- **Name**: `observations`
- **Shape**: `[batch_size, obs_dim]`
- **Type**: `float32`
- **Description**: Raw environment observations

### Output
- **Name**: `actions`
- **Shape**: `[batch_size, action_dim]`
- **Type**: `float32`
- **Description**: Denormalized actions ready for environment

### Dynamic Axes
- Batch size is dynamic, allowing inference on different batch sizes

## Architecture

The exported ONNX model contains:

```
Input: observations [batch_size, obs_dim]
    ↓
Observation Normalization (ObsRMS)
    ↓
MLP Network (ActorGaussian)
    ├── Hidden layers with ELU activation
    ├── Mean decoder → action_mean
    └── Std decoder → action_std (if not fixed)
    ↓
Action Sampling (deterministic by default)
    ↓
Action Denormalization
    ↓
Output: actions [batch_size, action_dim]
```

## Example Integration

### Python with ONNX Runtime

```python
import onnxruntime as ort
import numpy as np

# Load ONNX model
session = ort.InferenceSession("student_agent.onnx")

# Prepare input
observations = np.random.randn(1, obs_dim).astype(np.float32)
inputs = {session.get_inputs()[0].name: observations}

# Run inference
actions = session.run(None, inputs)[0]

print(f"Input shape: {observations.shape}")
print(f"Output shape: {actions.shape}")
```

### C++ with ONNX Runtime

```cpp
#include <onnxruntime_cxx_api.h>

// Initialize ONNX Runtime
Ort::Env env(ORT_LOGGING_LEVEL_WARNING, "StudentAgent");
Ort::Session session(env, "student_agent.onnx", Ort::SessionOptions{});

// Prepare input tensor
std::vector<float> input_data(obs_dim);
// ... fill input_data with observations ...

std::vector<int64_t> input_shape = {1, obs_dim};
auto memory_info = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);
Ort::Value input_tensor = Ort::Value::CreateTensor<float>(
    memory_info, input_data.data(), input_data.size(), 
    input_shape.data(), input_shape.size());

// Run inference
auto output_tensors = session.Run(Ort::RunOptions{}, 
    input_names.data(), &input_tensor, 1, 
    output_names.data(), 1);

// Get results
float* output_data = output_tensors[0].GetTensorMutableData<float>();
```

## Performance Notes

- **CPU Inference**: Optimized for CPU deployment with ONNX Runtime
- **Batch Processing**: Supports dynamic batch sizes for efficient processing
- **Memory Efficient**: All normalization parameters embedded in model
- **Deterministic**: Default export uses deterministic action selection

## Troubleshooting

### Common Issues

1. **Model Loading Failed**
   - Check that model checkpoint exists at specified path
   - Verify model_num matches available checkpoints

2. **ONNX Export Failed**
   - Ensure all dependencies are installed
   - Try exporting with CPU device first
   - Check for unsupported operations

3. **Accuracy Differences**
   - Small differences (<1e-5) are normal due to numerical precision
   - Large differences may indicate export issues

### Verification

The test script automatically verifies:
- Model loads correctly
- Outputs match PyTorch implementation
- Inference speed benchmarks

## Deployment

The exported ONNX model can be deployed on:
- **Edge devices** with ONNX Runtime
- **Mobile platforms** with ONNX Runtime Mobile
- **Web browsers** with ONNX.js
- **Embedded systems** with optimized ONNX Runtime builds

## Notes

- The export uses deterministic action selection by default
- For stochastic actions, modify the forward pass to accept epsilon as input
- Observation normalization statistics are frozen at export time
- Action bounds are embedded in the model for proper denormalization

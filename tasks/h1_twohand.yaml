# used to create the object
name: H1Twohand

backup_files: [
    'tasks/h1_twohand.yaml',
    'tasks/h1_twohand.py',
]
n_total_steps: 1000000000

sim:
    physics_engine: 'physx'
    sim_dt: 0.005
    con_dt: 0.02
    substeps: 1
    up_axis: "z"
    use_gpu_pipeline: True
    gravity: [0.0, 0.0, -9.81]
    physx:
        num_threads: 4
        solver_type: 1
        use_gpu: True
        num_position_iterations: 4
        num_velocity_iterations: 4
        contact_offset: 0.01
        rest_offset: 0.0
        bounce_threshold_velocity: 0.1
        max_depenetration_velocity: 1.0
        default_buffer_size_multiplier: 5.0
        max_gpu_contact_pairs: 8388608 # = 8*1024*1024
        num_subscenes: 4
        ##########################################
        # contact_collection
        # 0: CC_NEVER (don't collect contact info).
        # 1: CC_LAST_SUBSTEP (collect only contacts on last substep).
        # 2: CC_ALL_SUBSTEPS (broken - do not use!)
        contact_collection: 1
        ##########################################

env:
    num_envs: 1000
    env_spacing: 2.0
    history_len: 10
    enable_camera_sensors: False

    reward_names: [body_balance, vel, contact, energy, style, smooth_1st, smooth_2nd]
    cost_names: [gait, body_height, body_contact, joint_pos, joint_vel, joint_torque]
    stage_names: [stand, tilt, walk]

    urdf_asset:
        file: "h1/urdf/h1.urdf"
        fix_base_link: false
        flip_visual_attachments: true

    default_joint_positions:
        left_hip_yaw_joint: 0.0         # [rad]
        left_hip_roll_joint: 0.0        # [rad]
        left_hip_pitch_joint: -0.35     # [rad]
        left_knee_joint: 0.7            # [rad]
        left_ankle_joint: -0.35         # [rad]
        right_hip_yaw_joint: 0.0        # [rad]
        right_hip_roll_joint: 0.0       # [rad]
        right_hip_pitch_joint: -0.35    # [rad]
        right_knee_joint: 0.7           # [rad]
        right_ankle_joint: -0.35        # [rad]
        torso_joint: 0.0                # [rad]
        left_shoulder_pitch_joint: 0.0  # [rad]
        left_shoulder_roll_joint: 0.0   # [rad]
        left_shoulder_yaw_joint: 0.0    # [rad]
        left_elbow_joint: 0.0           # [rad]
        right_shoulder_pitch_joint: 0.0 # [rad]
        right_shoulder_roll_joint: 0.0  # [rad]
        right_shoulder_yaw_joint: 0.0   # [rad]
        right_elbow_joint: 0.0          # [rad]
    down_joint_positions:
        left_hip_yaw_joint: 0.0         # [rad]
        left_hip_roll_joint: 0.0        # [rad]
        left_hip_pitch_joint: -2.3      # [rad]
        left_knee_joint: 1.5            # [rad]
        left_ankle_joint: -0.8          # [rad]
        right_hip_yaw_joint: 0.0        # [rad]
        right_hip_roll_joint: 0.0       # [rad]
        right_hip_pitch_joint: -2.3     # [rad]
        right_knee_joint: 1.5           # [rad]
        right_ankle_joint: -0.8         # [rad]
        torso_joint: 0.0                # [rad]
        left_shoulder_pitch_joint: -1.4 # [rad]
        left_shoulder_roll_joint: 0.8   # [rad]
        left_shoulder_yaw_joint: 0.0    # [rad]
        left_elbow_joint: 1.0           # [rad]
        right_shoulder_pitch_joint: -1.4 # [rad]
        right_shoulder_roll_joint: -0.8 # [rad]
        right_shoulder_yaw_joint: 0.0   # [rad]
        right_elbow_joint: 1.0          # [rad]
    reverse_joint_positions:
        left_hip_yaw_joint: 0.0         # [rad]
        left_hip_roll_joint: 0.0        # [rad]
        left_hip_pitch_joint: 0.0       # [rad]
        left_knee_joint: 0.0            # [rad]
        left_ankle_joint: 0.0           # [rad]
        right_hip_yaw_joint: 0.0        # [rad]
        right_hip_roll_joint: 0.0       # [rad]
        right_hip_pitch_joint: 0.0      # [rad]
        right_knee_joint: 0.0           # [rad]
        right_ankle_joint: 0.0          # [rad]
        torso_joint: 0.0                # [rad]
        left_shoulder_pitch_joint: -2.5  # [rad]
        left_shoulder_roll_joint: 1.2   # [rad]
        left_shoulder_yaw_joint: 0.0    # [rad]
        left_elbow_joint: 0.4           # [rad]
        right_shoulder_pitch_joint: -2.5 # [rad]
        right_shoulder_roll_joint: -1.2  # [rad]
        right_shoulder_yaw_joint: 0.0   # [rad]
        right_elbow_joint: 0.4          # [rad]

    init_base_pose:
        pos: [0.0, 0.0, 1.1]            # x,y,z [m]
        quat: [0.0, 0.0, 0.0, 1.0]      # x,y,z,w [quat]
        lin_vel: [0.0, 0.0, 0.0]        # x,y,z [m/s]
        ang_vel: [0.0, 0.0, 0.0]        # x,y,z [rad/s]

    control:
        stiffness: 
            hip: 200.0
            knee: 300.0
            ankle: 40.0
            torso: 300.0
            shoulder: 200.0
            elbow: 200.0
        damping:                        # D gain [N*m*s/rad]
            hip: 5.0
            knee: 6.0
            ankle: 2.0
            torso: 6.0
            shoulder: 5.0
            elbow: 5.0
        action_scale: 0.25 # target angle = actionScale * action + defaultAngle
        action_smooth_weight: 0.5 # 1.0: no smoothing, 0.0: max smoothing

    learn:
        episode_length_s: 10.0   # [s]
        gait_frequency: 3        # [Hz]
        ########################
        # for constraint
        joint_pos_limit:
            upper: [
                0.2, 0.2, 2.53, 2.05, 0.52,
                0.2, 0.2, 2.53, 2.05, 0.52,
                0.2,
                2.87, 3.11, 4.45, 2.61, 
                2.87, 0.34, 1.3, 2.61, 
            ]
            lower: [
                -0.2, -0.2, -3.14, -0.26, -0.87,
                -0.2, -0.2, -3.14, -0.26, -0.87,
                -0.2,
                -2.87, -0.34, -1.3, -1.25,
                -2.87, -3.11, -4.45, -1.25,
            ]
        joint_vel_upper: [
            23, 23, 23, 14, 9,
            23, 23, 23, 14, 9,
            23,
            23, 23, 23, 23,
            23, 23, 23, 23,
        ]
        joint_torque_upper: [
            200, 200, 200, 300, 40,
            200, 200, 200, 300, 40,
            200,
            200, 200, 200, 200,
            200, 200, 200, 200,
        ]
        ########################
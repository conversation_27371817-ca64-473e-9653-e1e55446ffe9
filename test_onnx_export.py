#!/usr/bin/env python3
"""
Test script for the ONNX exported agent model.

This script demonstrates how to use the exported ONNX model
and compares its output with the original PyTorch agent.
"""

import numpy as np
import argparse
import os
from ruamel.yaml import YAM<PERSON>
from copy import deepcopy

# Import IsaacGym-related modules first (before torch)
from tasks import task_dict
from utils.wrapper import EnvWrapper
from utils import setSeed

# Import torch after IsaacGym modules
import torch

# Import algorithm modules after torch
from algos import algo_dict

try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("Warning: onnxruntime not available. Install with: pip install onnxruntime")


def compare_pytorch_vs_onnx(args, task_cfg, algo_cfg, onnx_path, num_tests=10):
    """
    Compare outputs between original PyTorch agent and ONNX model.
    """
    if not ONNX_AVAILABLE:
        print("Cannot perform comparison without onnxruntime")
        return
    
    if not os.path.exists(onnx_path):
        print(f"ONNX model not found: {onnx_path}")
        return
    
    print("Setting up PyTorch agent...")
    
    # Set seed for reproducibility
    setSeed(args.seed)
    
    # Create environment
    env_fn = lambda: task_dict[task_cfg['name']](
        cfg=task_cfg, rl_device=args.device_name, sim_device=args.device_name, 
        graphics_device_id=0, headless=True, 
        virtual_screen_capture=False, force_render=False
    )
    vec_env = EnvWrapper(env_fn)
    
    # Set up agent arguments
    args.device = vec_env.unwrapped.rl_device
    args.obs_dim = vec_env.unwrapped.num_obs
    args.action_dim = vec_env.unwrapped.num_acts
    args.action_bound_min = -np.ones(args.action_dim)
    args.action_bound_max = np.ones(args.action_dim)
    args.history_len = vec_env.unwrapped.history_len
    args.n_envs = 1
    
    # Create and load PyTorch agent
    agent_args = deepcopy(args)
    for key in algo_cfg.keys():
        agent_args.__dict__[key] = algo_cfg[key]
    
    agent = algo_dict[args.algo_name.lower()](agent_args)
    loaded_step = agent.load(args.model_num)
    
    if loaded_step == 0:
        raise ValueError(f"Failed to load agent model {args.model_num}")
    
    print(f"Loaded PyTorch agent from step {loaded_step}")
    
    # Load ONNX model
    print("Loading ONNX model...")
    ort_session = ort.InferenceSession(onnx_path)
    
    print(f"ONNX model inputs: {[input.name for input in ort_session.get_inputs()]}")
    print(f"ONNX model outputs: {[output.name for output in ort_session.get_outputs()]}")
    
    # Compare outputs
    print(f"\nComparing outputs for {num_tests} random observations...")
    
    max_diff = 0.0
    mean_diff = 0.0
    
    for i in range(num_tests):
        # Generate random observation
        # obs_tensor = torch.randn(1, args.obs_dim, dtype=torch.float32, device=args.device)
        obs_tensor =torch.tensor([0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, -0.0019, -0.0499, 0.9988, 0.0412, 0.8947, -1.7975, -0.1135, 0.9228, -1.6421, -0.1062, 0.9737, -1.8486, 0.1440, 0.9554, -1.7986, -0.0081, -0.0056, 0.0006, -0.0096, -0.0008, 0.0095, -0.0025, -0.0047, 0.0023, 0.0159, 0.0052, -0.0053, -0.8137, 0.8154, -0.3484, 0.3418, 0.8595, -0.4093, -0.7328, 0.9211, -1.2036, 1.0891, 0.8559, -0.4458, 1.0000, 0.0000, 0.0000])
        
        # Get PyTorch agent output
        with torch.no_grad():
            pytorch_action = agent.getAction(obs_tensor, deterministic=True)
        
        # Get ONNX model output
        obs_numpy = obs_tensor.cpu().numpy()
        ort_inputs = {ort_session.get_inputs()[0].name: obs_numpy}
        onnx_action = ort_session.run(None, ort_inputs)[0]
        
        # Compare
        pytorch_action_numpy = pytorch_action.cpu().numpy()
        diff = np.abs(pytorch_action_numpy - onnx_action).max()
        max_diff = max(max_diff, diff)
        mean_diff += diff
        
        if i < 3:  # Print first few comparisons
            print(f"Test {i+1}:")
            print(f"  PyTorch: {pytorch_action_numpy.flatten()[:5]}...")
            print(f"  ONNX:    {onnx_action.flatten()[:5]}...")
            print(f"  Max diff: {diff:.8f}")
    
    mean_diff /= num_tests
    
    print(f"\nComparison Results:")
    print(f"  Maximum difference: {max_diff:.8f}")
    print(f"  Mean difference: {mean_diff:.8f}")
    
    if max_diff < 1e-5:
        print("✓ ONNX export is accurate!")
    elif max_diff < 1e-3:
        print("⚠ ONNX export has small differences (likely acceptable)")
    else:
        print("✗ ONNX export has significant differences")
    
    # Clean up
    vec_env.close()


def benchmark_inference_speed(onnx_path, obs_dim, num_iterations=1000):
    """
    Benchmark inference speed of the ONNX model.
    """
    if not ONNX_AVAILABLE:
        print("Cannot benchmark without onnxruntime")
        return
    
    if not os.path.exists(onnx_path):
        print(f"ONNX model not found: {onnx_path}")
        return
    
    print(f"\nBenchmarking ONNX model inference speed...")
    
    # Load ONNX model
    ort_session = ort.InferenceSession(onnx_path)
    
    # Prepare test data
    test_obs = np.random.randn(1, obs_dim).astype(np.float32)
    ort_inputs = {ort_session.get_inputs()[0].name: test_obs}
    
    # Warmup
    for _ in range(10):
        ort_session.run(None, ort_inputs)
    
    # Benchmark
    import time
    start_time = time.time()
    
    for _ in range(num_iterations):
        ort_session.run(None, ort_inputs)
    
    end_time = time.time()
    
    total_time = end_time - start_time
    avg_time = total_time / num_iterations
    fps = 1.0 / avg_time
    
    print(f"Benchmark Results ({num_iterations} iterations):")
    print(f"  Total time: {total_time:.4f} seconds")
    print(f"  Average inference time: {avg_time*1000:.4f} ms")
    print(f"  Inference FPS: {fps:.1f}")


def main():
    parser = argparse.ArgumentParser(description="Test ONNX exported agent")
    parser.add_argument('--task_cfg_path', type=str, required=True, help='Task config file path')
    parser.add_argument('--algo_cfg_path', type=str, required=True, help='Algorithm config file path')
    parser.add_argument('--model_num', type=int, default=0, help='Model number to load')
    parser.add_argument('--onnx_path', type=str, default='student_agent.onnx', help='ONNX model path')
    parser.add_argument('--device_type', type=str, default='cpu', help='Device type (cpu/gpu)')
    parser.add_argument('--gpu_idx', type=int, default=0, help='GPU index')
    parser.add_argument('--seed', type=int, default=1, help='Random seed')
    parser.add_argument('--num_tests', type=int, default=10, help='Number of comparison tests')
    parser.add_argument('--benchmark', action='store_true', help='Run inference speed benchmark')
    
    args = parser.parse_args()
    
    # Load configurations
    with open(args.task_cfg_path, 'r') as f:
        task_cfg = YAML().load(f)
    args.task_name = task_cfg['name']
    
    with open(args.algo_cfg_path, 'r') as f:
        algo_cfg = YAML().load(f)
    args.algo_name = algo_cfg['name']
    
    # Set device
    if torch.cuda.is_available() and args.device_type == 'gpu':
        device_name = f'cuda:{args.gpu_idx}'
        print('[torch] Using CUDA')
    else:
        device_name = 'cpu'
        print('[torch] Using CPU')
    args.device_name = device_name
    
    # Set save directory for loading model
    args.name = f"{args.task_name.lower()}_{args.algo_name.lower()}"
    args.save_dir = f"results/{args.name}/seed_{args.seed}"
    
    # Run comparison
    compare_pytorch_vs_onnx(args, task_cfg, algo_cfg, args.onnx_path, args.num_tests)
    
    # Run benchmark if requested
    if args.benchmark:
        # We need to get obs_dim, so let's do a quick setup
        env_fn = lambda: task_dict[task_cfg['name']](
            cfg=task_cfg, rl_device=device_name, sim_device=device_name, 
            graphics_device_id=0, headless=True, 
            virtual_screen_capture=False, force_render=False
        )
        vec_env = EnvWrapper(env_fn)
        obs_dim = vec_env.unwrapped.num_obs
        vec_env.close()
        
        benchmark_inference_speed(args.onnx_path, obs_dim)


if __name__ == "__main__":
    main()

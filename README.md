# Stage-Wise CMORL

This is an official GitHub Repository for paper ["Stage-Wise Reward Shaping for Acrobatic Robots: A Constrained Multi-Objective Reinforcement Learning Approach"](https://arxiv.org/abs/2409.15755).

## Requirement

- python==3.7
- torch==1.12.1
- numpy==1.21.5
- isaacgym (https://developer.nvidia.com/isaac-gym)
- IsaacGymEnvs (https://github.com/isaac-sim/IsaacGymEnvs)
- ruamel.yaml
- requests
- pandas
- scipy
- wandb

## Organization
```
Stage-Wise-CMORL/
    └── algos/
    │     └── common/
    │     └── comoppo/
    │     └── student/
    └── assets/
    │     └── go1/
    │     └── h1/
    └── tasks/
    └── utils/
    └── main_student.py
    └── main_teacher.py
```
- `algos/`: contains the implementation of the proposed algorithm
- `assets/`: contains the assets of the robots
- `tasks/`: contains the implementation of the tasks
- `utils/`: contains the utility functions

## Tasks

- GO1 Robot (Quadruped from Unitree)
    - Back-Flip
    - Side-Flip
    - Side-Roll
    - Two-Hand Walk
- H1 Robot (Humanoid from Unitree)
    - Back-Flip
    - Two-Hand Walk

## Training and Evaluation

It is required to train a teacher poicy first, and then train a student policy using the teacher policy.

### Teacher Learning

- training: `python main_teacher.py --task_cfg_path tasks/{task_name}.yaml --algo_cfg_path algos/comoppo/{task_name}.yaml --wandb --seed 1`
- test: `python main_teacher.py --task_cfg_path tasks/{task_name}.yaml --algo_cfg_path algos/comoppo/{task_name}.yaml --test --render --seed 1 --model_num {saved_model_num}`

### Student Learning

- training: `python main_student.py --task_cfg_path tasks/{task_name}.yaml --algo_cfg_path algos/student/{task_name}.yaml --wandb --seed 1`
- test: `python main_student.py --task_cfg_path tasks/{task_name}.yaml --algo_cfg_path algos/student/{task_name}.yaml --test --render --seed 1 --model_num {saved_model_num}`


python main_teacher.py --task_cfg_path tasks/go1_backflip.yaml --algo_cfg_path algos/comoppo/go1_backflip.yaml --wandb --seed 1


python main_teacher.py --task_cfg_path tasks/go1_backflip.yaml  --algo_cfg_path algos/comoppo/go1_backflip.yaml  --test --render --seed 1 --model_num 100000000


python main_teacher.py --task_cfg_path tasks/robs3go_backflip.yaml --algo_cfg_path algos/comoppo/go1_backflip.yaml --wandb --seed 1
python main_teacher.py --task_cfg_path tasks/robs3go_backflip.yaml  --algo_cfg_path algos/comoppo/go1_backflip.yaml  --test --render --seed 1 --model_num 100000000

python main_teacher.py --task_cfg_path tasks/robs3go_twohand.yaml --algo_cfg_path algos/comoppo/go1_twohand.yaml --test --render --seed 1 --model_num 1000000000

python main_student.py --task_cfg_path tasks/robs3go_twohand.yaml --algo_cfg_path algos/student/go1_twohand.yaml --wandb --seed 1


python main_student.py --task_cfg_path tasks/robs3go_twohand.yaml  --algo_cfg_path algos/student/go1_twohand.yaml --wandb  --seed 42 --model_num 800000000
python main_student.py --task_cfg_path tasks/robs3go_twohand.yaml  --algo_cfg_path algos/student/go1_twohand.yaml --test --render --seed 1 --model_num 30000000

-m debugpy --listen 8934 --wait-for-client


export LD_LIBRARY_PATH=/home/<USER>/miniconda3/envs/cmorl/lib:$LD_LIBRARY_PATH



-m debugpy --listen 8934 --wait-for-client

obs: 42
    body_orns 3 [0 0 1]在body下的姿态
    dof_pos 12
    dof_vel 12
    prev_actions 12 
    commands 3
    self.obs_buf[:, :-self.raw_obs_dim] = self.obs_buf[:, self.raw_obs_dim:].clone()
    self.obs_buf[:, -self.raw_obs_dim:] = obs  放在最后
state : 21
    self.base_lin_vels  3
    self.base_ang_vels  3
    com_height        1
    foot_contacts  4
    gravities   3
    friction_coeffs  1
    restitution_coeffs  1 
    stages      5


self.cmd_time_buf 初始化为0
    land_masks = torch.logical_and(self.land_time_buf == 0, self.stage_buf[:, 4] == 1).type(torch.float32)  # 首次进入stage4
    self.land_time_buf[:] = land_masks*(self.progress_buf*self.control_dt) + (1.0 - land_masks)*self.land_time_buf # 记录首次进入stage4时间
    cmd_masks = torch.logical_and(self.cmd_time_buf == 0, self.stage_buf[:, 1] == 1).type(torch.float32)  # 首次进入stage1 下蹲
    self.cmd_time_buf[:] = cmd_masks*(self.progress_buf*self.control_dt) + (1.0 - cmd_masks)*self.cmd_time_buf # 记录stage1开始时间
command:3 每个时间步只有一个1
    commands = torch.zeros((len(env_ids), 3), dtype=torch.float32, device=self.device)
    masks0 = (self.cmd_time_buf[env_ids] == 0).type(torch.float32) # 还没进入下蹲阶段  即站立阶段
    masks1 = (1.0 - masks0)*(self.progress_buf[env_ids]*self.control_dt < self.cmd_time_buf[env_ids] + 0.2).type(torch.float32) # 下蹲准备跳
    masks2 = (1.0 - masks0)*(1.0 - masks1) # 调整姿态落地
    commands[:, 0] = masks0
    commands[:, 1] = masks1
    commands[:, 2] = masks2





self.joint_targets[:] = smooth_weight*(actions*self.action_scale + self.default_dof_positions) \
                        + (1.0 - smooth_weight)*self.joint_targets


stage_buf 管阶段 1表示当前阶段 0表示其他阶段
rew_buf 不同的reward


symmetric matrix  
        可以把关节完全左右对称
        self.joint_sym_mat = torch.zeros((self.num_dofs, self.num_dofs), device=self.device, dtype=torch.float32, requires_grad=False)
        self.joint_sym_mat[:3, 3:6] = torch.eye(3, device=self.device, dtype=torch.float32)
        self.joint_sym_mat[0, 3] = -1.0   # 左右hip是反的
        self.joint_sym_mat[3:6, :3] = torch.eye(3, device=self.device, dtype=torch.float32)
        self.joint_sym_mat[3, 0] = -1.0
        self.joint_sym_mat[6:9, 9:12] = torch.eye(3, device=self.device, dtype=torch.float32)
        self.joint_sym_mat[6, 9] = -1.0
        self.joint_sym_mat[9:12, 6:9] = torch.eye(3, device=self.device, dtype=torch.float32)
        self.joint_sym_mat[9, 6] = -1.0
        self.obs_sym_mat = torch.zeros((self.num_obs, self.num_obs), device=self.device, dtype=torch.float32, requires_grad=False)
        raw_obs_sym_mat = torch.eye(self.raw_obs_dim, device=self.device, dtype=torch.float32, requires_grad=False)
        raw_obs_sym_mat[1, 1] = -1.0  # base y轴对称
        for i in range(3):  # 填充 dof_pos dof_vel prev_actions 对称性
            raw_obs_sym_mat[(3+self.num_dofs*(i)):(3+self.num_dofs*(i+1)), (3+self.num_dofs*(i)):(3+self.num_dofs*(i+1))] = self.joint_sym_mat.clone()
        raw_obs_sym_mat[3+3*self.num_dofs:, 3+3*self.num_dofs:] = torch.eye(3, device=self.device, dtype=torch.float32)
        for i in range(self.history_len):
            self.obs_sym_mat[(self.raw_obs_dim*i):(self.raw_obs_dim*(i+1)), (self.raw_obs_dim*i):(self.raw_obs_dim*(i+1))] = raw_obs_sym_mat.clone()
        self.state_sym_mat = torch.eye(self.num_states - self.num_stages, device=self.device, dtype=torch.float32, requires_grad=False)
        self.state_sym_mat[1, 1] = -1.0
        self.state_sym_mat[3, 3] = -1.0
        self.state_sym_mat[5, 5] = -1.0
        self.state_sym_mat[7:11, 7:11] = 0  # 四个脚左右对称放
        self.state_sym_mat[7, 8] = 1.0
        self.state_sym_mat[8, 7] = 1.0
        self.state_sym_mat[9, 10] = 1.0
        self.state_sym_mat[10, 9] = 1.0
        self.state_sym_mat[12, 12] = -1.0
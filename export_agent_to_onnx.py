#!/usr/bin/env python3
"""
Export agent.getAction operation to ONNX format.

This script creates an ONNX model that replicates the agent.getAction functionality,
including observation normalization and action generation.
"""



# Import necessary modules from the project
from algos import algo_dict
from tasks import task_dict
from utils.wrapper import EnvWrapper
from utils import setSeed

import torch
import torch.nn as nn
import numpy as np
import argparse
import os
from ruamel.yaml import YAM<PERSON>
from copy import deepcopy




class StudentAgentONNX(nn.Module):
    """
    ONNX-exportable version of the student agent's getAction operation.
    
    This module combines observation normalization and action generation
    into a single forward pass suitable for ONNX export.
    """
    
    def __init__(self, agent):
        super(StudentAgentONNX, self).__init__()
        
        # Copy normalization parameters
        self.obs_dim = agent.obs_dim
        self.action_dim = agent.action_dim
        self.history_len = agent.history_len
        
        # Register normalization parameters as buffers (will be included in ONNX)
        raw_obs_dim = agent.obs_rms.raw_obs_dim
        self.register_buffer('obs_mean', agent.obs_rms.cur_mean.clone())
        self.register_buffer('obs_var', agent.obs_rms.cur_var.clone())
        
        # Copy actor network
        self.actor = deepcopy(agent.actor)
        
        # Copy action bounds for denormalization
        self.register_buffer('action_bound_min', torch.tensor(agent.action_bound_min, dtype=torch.float32))
        self.register_buffer('action_bound_max', torch.tensor(agent.action_bound_max, dtype=torch.float32))
        
    def normalize_observations(self, observations):
        """
        Normalize observations using running mean and variance.
        Replicates the ObsRMS.normalize() method.
        """
        # Reshape observations to match normalization format
        reshaped_obs = observations.view(-1, self.obs_dim)
        
        # Tile mean and variance for history length
        reshaped_mean = self.obs_mean.view(1, -1).repeat(1, self.history_len)
        reshaped_var = self.obs_var.view(1, -1).repeat(1, self.history_len)
        
        # Normalize
        norm_obs = (reshaped_obs - reshaped_mean) / torch.sqrt(reshaped_var + 1e-8)
        
        return norm_obs.view_as(observations)
    
    def unnormalize_actions(self, norm_actions):
        """
        Convert normalized actions [-1, 1] to actual action bounds.
        Replicates the unnormalize function from actor_base.py.
        """
        return (norm_actions + 1.0) * 0.5 * (self.action_bound_max - self.action_bound_min) + self.action_bound_min
    
    def forward(self, observations, deterministic=True):
        """
        Forward pass that replicates agent.getAction() functionality.
        
        Args:
            observations: Input observations tensor
            deterministic: Whether to use deterministic action (True) or stochastic (False)
            
        Returns:
            unnorm_actions: Denormalized actions ready for environment
        """
        # Step 1: Normalize observations
        norm_obs = self.normalize_observations(observations)
        
        # Step 2: Get action distribution from actor
        action_mean, action_log_std, action_std = self.actor.forward(norm_obs)
        
        # Step 3: Sample action (deterministic or stochastic)
        if deterministic:
            norm_action = action_mean
        else:
            # For stochastic sampling, we need epsilon
            # In ONNX export, we'll use deterministic by default
            # If stochastic is needed, epsilon should be provided as input
            epsilon = torch.randn_like(action_mean)
            norm_action = action_mean + epsilon * action_std
        
        # Step 4: Denormalize actions
        unnorm_actions = self.unnormalize_actions(norm_action)
        
        return unnorm_actions


def export_agent_to_onnx(args, task_cfg, algo_cfg, output_path="policy.onnx"):
    """
    Export the student agent's getAction operation to ONNX format.
    """
    print("Setting up environment and agent...")
    
    # Set seed for reproducibility
    setSeed(args.seed)
    
    # Create environment to get dimensions
    env_fn = lambda: task_dict[task_cfg['name']](
        cfg=task_cfg, rl_device=args.device_name, sim_device=args.device_name, 
        graphics_device_id=0, headless=True, 
        virtual_screen_capture=False, force_render=False
    )
    vec_env = EnvWrapper(env_fn)
    
    # Set up agent arguments
    args.device = vec_env.unwrapped.rl_device
    args.n_envs = vec_env.unwrapped.num_envs
    args.obs_dim = vec_env.unwrapped.num_obs
    args.action_dim = vec_env.unwrapped.num_acts
    args.action_bound_min = -np.ones(args.action_dim)
    args.action_bound_max = np.ones(args.action_dim)
    args.history_len = vec_env.unwrapped.history_len
    
    # Create and load agent
    agent_args = deepcopy(args)
    for key in algo_cfg.keys():
        agent_args.__dict__[key] = algo_cfg[key]
    
    agent = algo_dict[args.algo_name.lower()](agent_args)
    loaded_step = agent.load(args.model_num)
    
    if loaded_step == 0:
        raise ValueError(f"Failed to load agent model {args.model_num}")
    
    print(f"Successfully loaded agent model from step {loaded_step}")
    
    # Create ONNX-exportable model
    onnx_model = StudentAgentONNX(agent)
    onnx_model.eval()
    
    # Create dummy input for ONNX export
    # Use single environment for export (batch_size=1)
    dummy_obs = torch.randn(1, args.obs_dim, dtype=torch.float32, device=args.device)
    
    print(f"Exporting to ONNX with input shape: {dummy_obs.shape}")
    print(f"Output path: {output_path}")
    
    # Export to ONNX
    torch.onnx.export(
        onnx_model,
        dummy_obs,
        output_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['obs'],
        output_names=['actions'],
        # dynamic_axes={
        #     'observations': {0: 'batch_size'},
        #     'actions': {0: 'batch_size'}
        # }
    )
    
    print(f"Successfully exported agent to {output_path}")
    
    # Verify the export by loading and testing
    print("Verifying ONNX export...")
    try:
        import onnx
        import onnxruntime as ort
        
        # Load and check ONNX model
        onnx_model_check = onnx.load(output_path)
        onnx.checker.check_model(onnx_model_check)
        
        # Test inference
        ort_session = ort.InferenceSession(output_path)
        test_obs = np.random.randn(1, args.obs_dim).astype(np.float32)
        ort_inputs = {ort_session.get_inputs()[0].name: test_obs}
        ort_outputs = ort_session.run(None, ort_inputs)
        
        print(f"ONNX model verification successful!")
        print(f"Input shape: {test_obs.shape}")
        print(f"Output shape: {ort_outputs[0].shape}")
        
    except ImportError:
        print("Warning: onnx and onnxruntime not available for verification")
        print("Install with: pip install onnx onnxruntime")
    except Exception as e:
        print(f"ONNX verification failed: {e}")
    
    # Clean up
    vec_env.close()
    
    return output_path


def main():
    parser = argparse.ArgumentParser(description="Export student agent to ONNX")
    parser.add_argument('--task_cfg_path', type=str, required=True, help='Task config file path')
    parser.add_argument('--algo_cfg_path', type=str, required=True, help='Algorithm config file path')
    parser.add_argument('--model_num', type=int, default=0, help='Model number to load')
    parser.add_argument('--output_path', type=str, default='student_agent.onnx', help='Output ONNX file path')
    parser.add_argument('--device_type', type=str, default='cpu', help='Device type (cpu/gpu)')
    parser.add_argument('--gpu_idx', type=int, default=0, help='GPU index')
    parser.add_argument('--seed', type=int, default=1, help='Random seed')
    
    args = parser.parse_args()
    
    # Load configurations
    with open(args.task_cfg_path, 'r') as f:
        task_cfg = YAML().load(f)
    args.task_name = task_cfg['name']
    
    with open(args.algo_cfg_path, 'r') as f:
        algo_cfg = YAML().load(f)
    args.algo_name = algo_cfg['name']
    
    # Set device
    if torch.cuda.is_available() and args.device_type == 'gpu':
        device_name = f'cuda:{args.gpu_idx}'
        print('[torch] Using CUDA')
    else:
        device_name = 'cpu'
        print('[torch] Using CPU')
    args.device_name = device_name
    
    # Set save directory for loading model
    args.name = f"{args.task_name.lower()}_{args.algo_name.lower()}"
    args.save_dir = f"results/{args.name}/seed_{args.seed}"
    
    # Export to ONNX
    output_path = export_agent_to_onnx(args, task_cfg, algo_cfg, args.output_path)
    print(f"\nExport completed successfully: {output_path}")


if __name__ == "__main__":
    main()
